//葛帆

import JsonDB from '../module/JsonDB'
import * as fs from 'fs'
import API from '../module/API'
import { getAPPBasePath, getBaseCachePath, getNextKey, getRoomBaseCachePath } from '../module/API'
import { join } from 'path'
import os from 'os'
import { importFlv } from './utils/importVideo'
// eslint-disable-next-line @typescript-eslint/no-require-imports
const checkDiskSpace = require('check-disk-space').default
// const checkDiskSpace = require('check-disk-space')
import { app, BrowserWindow, IpcMain, dialog } from 'electron'
import path from 'path'
import { getAllRoomCoreData } from './business/RoomService'

// 日志条目接口
interface LogEntry {
  timestamp: number
  level: string
  content: string
  id: string
}

//创建类
export default class GFService {
  // 日志文件路径
  private static LOG_FILE_PATH: string = path.join(getAPPBasePath(), 'logs', 'log.json')
  public static hook(ipcMain: IpcMain): void {
    // 测试用例，下载房间数据
    // ipcMain.on('/baiying/download', (event, { room_id }) => {
    //   console.log('开始下载房间数据')
    //   // 定义下载状态文本数组
    //   const downloadTexts = ['准备', '正在', '加速', '即将完成']
    //   let textIndex = 0
    //   let innertext = downloadTexts[0]

    //   // 创建定时器，每2秒更新一次状态文本
    //   const statusTimer = setInterval(() => {
    //     textIndex = (textIndex + 1) % downloadTexts.length
    //     innertext = downloadTexts[textIndex]
    //     event.reply('update_status', { type: 'notice', text: innertext + '下载', roomid: room_id })

    //     // 如果状态是"即将完成"，则模拟下载完成并清除定时器
    //     if (innertext === '即将完成') {
    //       // 清除定时器
    //       // 延迟一点时间后发送下载完成的通知
    //       setTimeout(() => {
    //         event.reply('update_status', { type: 'success', text: '下载完成', roomid: room_id })
    //         clearInterval(statusTimer)
    //       }, 1000)
    //     }
    //   }, 2000)

    //   // 初始状态通知
    //   event.reply('update_status', { type: 'notice', text: innertext + '下载', roomid: room_id })
    // })

    // 获取所有日志
    ipcMain.handle('get-all-logs', () => {
      try {
        // 确保日志目录存在
        const logDir = path.dirname(this.LOG_FILE_PATH)
        if (!fs.existsSync(logDir)) {
          fs.mkdirSync(logDir, { recursive: true })
          // console.log('创建日志目录:', logDir);
          return { success: true, data: [] }
        }

        if (!fs.existsSync(this.LOG_FILE_PATH)) {
          // console.log('日志文件不存在，返回空数组');
          return { success: true, data: [] }
        }

        const fileContent = fs.readFileSync(this.LOG_FILE_PATH, 'utf-8')
        const logs = JSON.parse(fileContent)
        // console.log('读取日志文件成功，日志数量:', logs.length);
        return { success: true, data: logs }
      } catch (error) {
        // console.error('读取日志文件失败:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        }
      }
    })

    // 添加新日志
    ipcMain.handle('add-log', (_, level: string, content: string) => {
      try {
        this.writeLog(level, content)
        return { success: true }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        }
      }
    })

    // // 清空日志
    ipcMain.handle('clear-logs', () => {
      try {
        fs.writeFileSync(this.LOG_FILE_PATH, JSON.stringify([]), 'utf-8')
        return { success: true }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        }
      }
    })

    // 监听渲染进程的调用
    ipcMain.handle('select-video-file', async () => {
      const result = await dialog.showOpenDialog({
        title: '请选择 .flv 格式视频文件',
        filters: [{ name: 'FLV 视频', extensions: ['flv'] }],
        properties: ['openFile']
      })
      if (result.canceled || result.filePaths.length === 0) {
        return null
      }
      return result.filePaths[0]
    })

    // 选择Excel文件
    ipcMain.handle('select-excel-file', async () => {
      const result = await dialog.showOpenDialog({
        title: '请选择 Excel 文件',
        filters: [
          { name: 'Excel 文件', extensions: ['xlsx', 'xls'] },
          { name: '所有文件', extensions: ['*'] }
        ],
        properties: ['openFile']
      })
      if (result.canceled || result.filePaths.length === 0) {
        return null
      }
      return result.filePaths[0]
    })

    ipcMain.on('/detail/getData', async (event, id) => {
      const roomid = 'room_' + id

      const detail = JsonDB.getRecord(getBaseCachePath() + roomid)
      const roomList = await getAllRoomCoreData()

      // 检查每个房间的本地文件是否存在，不存在的添加标识

      const nextroom = getNextKey(id, roomList)
      const roomid2 = 'room_' + nextroom
      const room2data = JsonDB.getRecord(getBaseCachePath() + roomid2)
      //获取electron 应用程序的运行根目录
      // 获取视频路径（兼容 .m3u8 和 .flv）
      const videoDir = getRoomBaseCachePath(id, 'video_')
      let videolink = ''

      const m3u8Path = join(videoDir, 'video.m3u8')
      const flvPath = join(videoDir, 'video.flv')

      if (fs.existsSync(m3u8Path)) {
        videolink = m3u8Path
      } else if (fs.existsSync(flvPath)) {
        videolink = flvPath
      } else {
        console.warn(`未找到视频文件: ${m3u8Path} 或 ${flvPath}`)
        videolink = '' // 或默认值
      }
      detail.videolink = videolink
      event.reply('/detail/reply', [
        detail,
        room2data,
        roomList.map((room) => {
          const roomFilePath = getBaseCachePath() + 'room_' + room.live_id
          if (!fs.existsSync(roomFilePath)) {
            room.missingFile = true
          } else {
            room.missingFile = false
          }
          return room
        })
      ])
    })

    // 上传视频
    ipcMain.on('import-video', async (event, { date, title, originalPath, startTime, endTime }) => {
      console.log('import-video', { date, title, originalPath, startTime, endTime })
      const baseDir = path.join(getAPPBasePath(), `cache/video_${date}`)
      const targetFlv = path.join(baseDir, 'video.flv')
      try {
        if (!fs.existsSync(baseDir)) {
          fs.mkdirSync(baseDir, { recursive: true })
        }
        // 拷贝视频文件
        fs.copyFileSync(originalPath, targetFlv)
        // 调用导入函数，传递startTime和endTime参数
        await importFlv(date, title, targetFlv, startTime, endTime)
        // 通知渲染进程成功
        event.sender.send('import-video-success', {
          title,
          date,
          flvPath: targetFlv
        })
      } catch (err: any) {
        event.sender.send('import-video-failure', err.toString())
      }
    })

    ipcMain.on('on_audio2srt', async (event, arg) => {
      const video2m4aPath = fs.existsSync(`cache/video_${arg.roomid}/video.m4a`)
      const hls_url = getAPPBasePath() + `cache/video_${arg.roomid}/video.m3u8`
      const m4a_path_url = getAPPBasePath() + `cache/video_${arg.roomid}/video.m4a`

      const ifsegs = JsonDB.getItem(`cache/room_${arg.roomid}`, 'segs', [])
      if (!ifsegs.length) {
        try {
          // 判断音频文件是否存在
          if (!video2m4aPath) {
            await API.video2m4a(hls_url, m4a_path_url)
          }
          const audio2seg = await API.audio2seg(m4a_path_url)
          JsonDB.setItem(`cache/room_${arg.roomid}`, 'segs', audio2seg)
        } catch (e) {
          event.reply('error_service', e + `，文件：${arg.roomid}`)
          // 回复消息app页面通知loading状态修改
          event.reply('app_error_service', e + `，文件：${arg.roomid}`)
        }
      }
      const segs = JsonDB.getItem(`cache/room_${arg.roomid}`, 'segs')
      // console.log('segs:', segs);
      event.reply('on_audio2srt', segs)
      // 对app文件发送消息，修改loading状态
      event.reply('app_on_audio2srt', segs)
    })

    // 获取 Electron 应用的安装路径，读取磁盘大小
    ipcMain.on('check-disk-space', async (event) => {
      try {
        const appPath = app.getAppPath()
        const driveRoot = path.parse(appPath).root // 自动解析
        const result = await checkDiskSpace(driveRoot)
        event.reply('check-disk-space-reply', {
          free: result.free,
          size: result.size,
          path: result.diskPath
        })
      } catch (err: any) {
        event.reply('check-disk-space-reply', { error: err.message })
      }
    })

    // 打印字幕
    ipcMain.on('print-html', (event, htmlContent) => {
      const fullHtml = `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
          <meta charset="UTF-8" />
          <title>打印预览</title>
          <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet" />
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial;
              padding: 40px;
              font-size: 16px;
            }
            h5 {
              color: #28a745;
            }
          </style>
        </head>
        <body>
        ${htmlContent}
        </body>
        </html>
      `
      const tempPath = path.join(os.tmpdir(), 'print_temp.html')
      fs.writeFileSync(tempPath, fullHtml, 'utf8')

      const printWindow = new BrowserWindow({
        width: 800,
        height: 600,
        show: true, //打开可见预览，关闭不可见
        webPreferences: {
          contextIsolation: false, // 这里必须为 false，确保渲染器可以正常运行
          sandbox: false
        }
      })

      printWindow.loadFile(tempPath)
      // 用于排查调试
      // printWindow.webContents.openDevTools({ mode: 'detach' });
      printWindow.webContents.on('did-finish-load', () => {
        setTimeout(() => {
          if (printWindow && !printWindow.isDestroyed()) {
            printWindow.webContents.print(
              {
                silent: false,
                printBackground: true,
                pageSize: 'A4'
              },
              (success, err: any) => {
                // success: true 表示打印已发送到打印机队列，但不代表用户没有取消
                // err: 如果用户取消打印，err.message 通常为 "Printing cancelled"
                let msg = ''
                if (!success) {
                  // 打印失败或被取消
                  if (err) {
                    // 有些情况下 err.message 可能为 undefined，但 err.toString() 可能有值
                    const errMsg =
                      typeof err.message === 'string' && err.message
                        ? err.message
                        : typeof err === 'string'
                          ? err
                          : err && err.toString
                            ? err.toString()
                            : ''
                    if (errMsg && errMsg.toLowerCase().includes('cancel')) {
                      msg = '打印已取消'
                    } else if (errMsg) {
                      msg = '打印失败: ' + errMsg
                    } else {
                      msg = '打印失败'
                    }
                  } else {
                    msg = '打印失败'
                  }
                  event.reply('print-result', msg)
                } else {
                  // success为true，但err可能有内容（如警告），通常打印成功
                  event.reply('print-result', '打印成功')
                }
                console.log('err.message:', err && err.message, 'err:', err, 'success:', success)

                // 再次判断，防止 printWindow 已关闭
                if (printWindow && !printWindow.isDestroyed()) {
                  printWindow.close()
                }
              }
            )
          }
        }, 1000)
      })

      printWindow.webContents.on('did-fail-load', () => {
        // 加载失败也需要返回消息
        event.reply('print-result', '打印窗口加载失败')
        if (printWindow && !printWindow.isDestroyed()) {
          printWindow.close()
        }
      })
    })
  }
  public static mergeCommentsIntoRoomFile(roomid: string): any {
    // ipcMain.on('merge-comments-into-room-file', async (event, roomid: string) => {
    const commentDir = path.join('cache', `comments_${roomid}`)
    const roomFilePath = path.join('cache', `room_${roomid}`)

    // console.log(`开始合并评论，roomid: ${roomid}`);
    // console.log(`评论目录路径: ${commentDir}`);
    // console.log(`目标文件路径: ${roomFilePath}`);

    if (!fs.existsSync(commentDir)) {
      console.warn(`评论目录不存在: ${commentDir}`)
      // event.reply('merge-comments-into-room-file-reply', {
      //   success: false,
      //   message: '评论目录不存在'
      // });
      return
    }

    const allComments: any[] = []

    try {
      const files = fs.readdirSync(commentDir).filter((file) => {
        const filePath = path.join(commentDir, file)
        return fs.statSync(filePath).isFile()
      })
      for (const file of files) {
        const filePath = path.join(commentDir, file)
        const content = fs.readFileSync(filePath, 'utf-8')
        const data = JSON.parse(content)
        // console.log(content)
        if (Array.isArray(data.list.comments)) {
          // console.log(data.list.comments)
          data.list.comments.forEach((comment: any) => {
            allComments.push({
              content: comment.content,
              event_ts: parseInt(file) / 1000,
              comment_tag: comment.comment_tag,
              is_important: false,
              nick_name: comment.nick_name
            })
          })
        }
      }

      console.log(`读取到 ${allComments.length} 条评论`)

      let roomData: any = {}
      if (fs.existsSync(roomFilePath)) {
        const content = fs.readFileSync(roomFilePath, 'utf-8')
        roomData = JSON.parse(content)
      }

      roomData.comments = allComments

      // 异步写入，避免阻塞主进程
      fs.promises.writeFile(roomFilePath, JSON.stringify(roomData, null, 2), 'utf-8')

      console.log(`成功写入 ${allComments.length} 条评论到 room 文件`)
      return allComments
      // event.reply('merge-comments-into-room-file-reply', {
      //   success: true,
      //   count: allComments.length
      // });
    } catch (e) {
      console.error('合并评论失败:', e)
      // event.reply('merge-comments-into-room-file-reply', {
      //   success: false,
      //   message: e.message
      // });
    }
    // });
  }

  /**
   * 写入日志到文件
   * @param level 日志等级 ('info' | 'warn' | 'error')
   * @param content 日志内容
   */
  public static writeLog(level: string, content: string): void {
    try {
      // 确保日志目录存在
      const logDir = path.dirname(this.LOG_FILE_PATH)
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true })
        // console.log('创建日志目录:', logDir);
      }

      // 读取现有日志或创建新的日志数组
      let logs: LogEntry[] = []
      if (fs.existsSync(this.LOG_FILE_PATH)) {
        const fileContent = fs.readFileSync(this.LOG_FILE_PATH, 'utf-8')
        try {
          logs = JSON.parse(fileContent)
          // console.log('读取现有日志文件，当前日志数量:', logs.length);
        } catch (e) {
          console.error('解析日志文件失败，创建新的日志文件', e)
        }
      } else {
        // console.log('日志文件不存在，将创建新文件');
      }

      // 添加新日志
      logs.push({
        timestamp: Date.now(),
        level,
        content,
        id: `log-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
      })

      // 如果日志数量过多，只保留最新的1000条
      if (logs.length > 1000) {
        logs = logs.slice(-1000)
      }

      // 写入文件
      fs.writeFileSync(this.LOG_FILE_PATH, JSON.stringify(logs, null, 2), 'utf-8')
      // console.log(`日志已写入: [${level}] ${content}`);
    } catch (error) {
      console.error('写入日志失败:', error)
    }
  }
}
