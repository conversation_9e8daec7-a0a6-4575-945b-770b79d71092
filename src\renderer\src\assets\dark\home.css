.dark .dashboard-content .section-metrics {
  margin-bottom: 16px !important;
}

.dark .card-header .card-header-title {
  color: var(--title-text-color);
  font-size: var(---title-text-size);
}

.dark .card-title p {
  color: var(--card-title-color);
  font-size: var(--title-text-size);
  font-family: var(--card-title-family);
}

.dark .card-title h4 {
  color: var(--title-text-color);
  font-size: var(--card-title-size);
  font-family: var(--card-title-family);
  font-weight: var(--card-title-weight);
}

.dark .routerlink-btn {
  background-color: var(--primary-color);
  color: var(--title-color);
  border-radius: var(--btn-radius);
  height: 25px;
  border: 0;
}

.dark .action-buttons button {
  background-color: var(--shadow-color);
  color: var(--title-color);
  border-radius: var(--btn-radius);
  height: 25px;
}


.dark .el-range-editor--small.el-input__wrapper {
  height: 32px;
  width: 266px;
}

.dark .el-range-editor--small.el-input__wrapper input {
  color: var(--title-color);
}

.dark .el-date-editor .el-range__icon {
  color: var(--title-color);
}

.dark .custom-date-picker .el-date-editor {
  box-shadow: none !important;
  background-color: var(--popover-bg-color);
}

.dark .card {
  border-radius: 7.6px;
}

.dark .toolbox-content button {
  background-color: var(--shadow-color) !important;
  border: 0px;
  color: var(--title-color);
  font-size: var(--title-text-size);
  font-weight: normal;
}

/* .dark .dashboard-content .darkhr {
  margin-left: -24px !important;
  margin-right: -24px !important;
} */

.dark .sidebar-header .brand-text h6 {
  color: var(--title-text-color) !important;
}

.dark .sidebar-header {
  background-color: var(--bg-color) !important;
}

.dark .header-left h5 {
  color: var(--title-text-color);
  font-size: 18px;
}

.dark .header-container nav {
  background-color: var(--bg-color) !important;
  border: 0;
}

.dark .content-container .main-content {
  background-color: var(--floor-bg-color) !important;
}

.dark .sidebar-container .sidebar {
  background-color: var(--bg-color) !important;
}

.dark .header-container .loginstate button {
  color: var(--middle-text-color);
  background-color: var(--popover-bg-color);
  border: 0;
  border-radius: 4px;
}

.dark .header-container .themestate button {
  color: var(--middle-text-color);
}

.dark .sidebar-nav {
  background-color: var(--bg-color);
}

.dark .sidebar-nav .router-link-exact-active {
  color: var(--title-text-color);
  background-color: var(--popover-bg-color);
  box-shadow: 0 0 0;
  font-weight: normal;
}

.dark .sidebar-nav .router-link-exact-active:hover {
  color: var(--middle-text-color);
  background-color: var(--popover-bg-color) !important;
}

.dark .sidebar-nav .nav-link:hover {
  color: var(--middle-text-color);
  background-color: var(--popover-bg-color) !important;
}

.dark .time-span-selector div button {
  /* background-color: var(--button-background-color); */
  color: var(--title-color);
  border-radius: var(--btn-radius);
  border: 0;
  height: 33px;
  box-shadow: 0 0 0;
}

.dark .time-span-selector .btn-outline-primary {
  background: hsla(0, 0%, 100%, 0.2);
  color: var(--title-color);
  border-radius: var(--btn-radius);
  height: 33px;
  border: 0;
}

.dark .time-span-selector a {
  color: var(--title-color);
  font-size: 12px;
}

.dark .card-title-style {
  background-image: url('@/assets/img/bg.svg');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 4px;
}

.dark .card-title .title-p {
  color: #fff;
  font-size: 20px;
}

.dark .card-title .title-f {
  color: #fff;
  font-size: 44px;
  font-weight: 700;
}

.dark .about-us-center span {
  color: #fff;
}

.dark .modal-content {
  background-color: #fff;
}

.dark .modal .btn-close {
  background-color: #fff;
}
