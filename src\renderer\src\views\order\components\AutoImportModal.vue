<template>
  <el-dialog
    v-model="visible"
    title="自动导入Excel数据"
    width="800px"
    top="5vh"
    destroy-on-close
    class="auto-import-dialog"
    @close="handleClose"
  >
    <!-- 步骤指示器 -->
    <el-steps :active="currentStep" align-center style="margin-bottom: 30px">
      <el-step title="输入URL" />
      <el-step title="下载文件" />
      <el-step title="处理数据" />
    </el-steps>

    <!-- 步骤 1: URL输入 -->
    <div v-if="currentStep === 0">
      <div class="url-input-area">
        <el-form ref="urlFormRef" :model="urlForm" :rules="urlRules" label-width="100px">
          <el-form-item label="Excel文件URL" prop="url">
            <el-input
              v-model="urlForm.url"
              placeholder="请输入Excel文件的下载链接"
              type="textarea"
              :rows="3"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <div class="url-tips">
              <el-alert title="提示" type="info" :closable="false" show-icon>
                <template #default>
                  <ul style="margin: 0; padding-left: 20px">
                    <li>请确保URL指向有效的Excel文件（.xlsx或.xls格式）</li>
                    <li>文件大小建议不超过10MB</li>
                    <li>支持HTTP和HTTPS协议</li>
                    <li>确保网络连接稳定</li>
                    <li>下载的文件将自动验证格式并导入数据</li>
                    <li>临时文件会在处理完成后自动清理</li>
                  </ul>
                </template>
              </el-alert>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 步骤 2: 下载进度 -->
    <div v-if="currentStep === 1">
      <div class="download-progress-area">
        <el-result icon="info" title="正在下载Excel文件" :sub-title="downloadProgress.message">
          <template #extra>
            <div style="width: 100%; max-width: 400px; margin: 0 auto">
              <el-progress
                :percentage="downloadProgress.percentage"
                :status="downloadProgress.percentage === 100 ? 'success' : undefined"
                :stroke-width="8"
                text-inside
              />
              <div style="margin-top: 10px; text-align: center; color: #666; font-size: 14px">
                {{
                  downloadProgress.stage === 'downloading' ? '下载中...' : downloadProgress.message
                }}
              </div>
            </div>
          </template>
        </el-result>
      </div>
    </div>

    <!-- 步骤 3: 处理进度 -->
    <div v-if="currentStep === 2">
      <div class="process-progress-area">
        <el-result icon="info" title="正在处理Excel数据" :sub-title="processProgress.message">
          <template #extra>
            <div style="width: 100%; max-width: 500px; margin: 0 auto">
              <!-- 处理进度条 -->
              <el-progress
                :percentage="processProgress.percentage"
                :status="processProgress.percentage === 100 ? 'success' : undefined"
                :stroke-width="8"
                text-inside
              />

              <!-- 详细信息 -->
              <div style="margin-top: 20px">
                <div class="progress-stats">
                  <div class="stat-item">
                    <span class="stat-label">已处理:</span>
                    <span class="stat-value"
                      >{{ processProgress.processedRows }}/{{ processProgress.totalRows }}</span
                    >
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">有效记录:</span>
                    <span class="stat-value text-success">{{ processProgress.validRows }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">错误记录:</span>
                    <span class="stat-value text-danger">{{ processProgress.errorRows }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">已导入:</span>
                    <span class="stat-value text-primary">{{ processProgress.importedRows }}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-result>
      </div>
    </div>

    <!-- 步骤 4: 完成结果 -->
    <div v-if="currentStep === 3">
      <div class="result-area">
        <el-result
          :icon="processResult?.successCount > 0 ? 'success' : 'warning'"
          :title="processResult?.successCount > 0 ? '导入完成' : '导入失败'"
          :sub-title="`成功导入 ${processResult?.successCount || 0} 条记录，失败 ${processResult?.failedCount || 0} 条记录`"
        >
          <template #extra>
            <!-- 统计信息 -->
            <div class="result-stats">
              <div class="stat-card">
                <div class="stat-number">{{ processResult?.totalRows || 0 }}</div>
                <div class="stat-label">总记录数</div>
              </div>
              <div class="stat-card success">
                <div class="stat-number">{{ processResult?.successCount || 0 }}</div>
                <div class="stat-label">成功导入</div>
              </div>
              <div class="stat-card danger">
                <div class="stat-number">{{ processResult?.failedCount || 0 }}</div>
                <div class="stat-label">导入失败</div>
              </div>
            </div>

            <!-- 错误详情 -->
            <div v-if="processResult?.sampleErrors && processResult.sampleErrors.length > 0">
              <el-button type="text" @click="showErrorDetails = !showErrorDetails">
                {{ showErrorDetails ? '隐藏' : '查看' }}错误详情
              </el-button>
              <el-collapse v-if="showErrorDetails" style="margin-top: 15px; text-align: left">
                <el-collapse-item title="错误记录详情" name="errors">
                  <el-table :data="processResult.sampleErrors" size="small" max-height="200" border>
                    <el-table-column prop="rowIndex" label="行号" width="80" />
                    <el-table-column prop="errors" label="错误信息">
                      <template #default="{ row }">
                        <el-tag
                          v-for="error in row.errors"
                          :key="error"
                          type="danger"
                          size="small"
                          style="margin-right: 5px"
                        >
                          {{ error }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-collapse-item>
              </el-collapse>
            </div>

            <div style="margin-top: 20px">
              <el-button type="primary" @click="handleClose">完成</el-button>
            </div>
          </template>
        </el-result>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="currentStep === 0"
          type="primary"
          :disabled="!urlForm.url.trim()"
          :loading="downloading"
          @click="handleStartDownload"
        >
          {{ downloading ? '下载中...' : '开始下载' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const currentStep = ref(0)
const downloading = ref(false)
const processing = ref(false)
const showErrorDetails = ref(false)

// URL表单
const urlForm = ref({
  url: ''
})

const urlFormRef = ref()

// URL验证规则
const urlRules = {
  url: [
    { required: true, message: '请输入Excel文件URL', trigger: 'blur' },
    {
      validator: (_rule: any, value: string, callback: any) => {
        if (!value) {
          callback()
          return
        }
        try {
          const url = new URL(value)
          if (!['http:', 'https:'].includes(url.protocol)) {
            callback(new Error('仅支持HTTP和HTTPS协议'))
            return
          }
          callback()
        } catch {
          callback(new Error('请输入有效的URL'))
        }
      },
      trigger: 'blur'
    }
  ]
}

// 下载进度
const downloadProgress = ref({
  stage: 'waiting',
  message: '准备下载...',
  percentage: 0
})

// 处理进度
const processProgress = ref({
  processedRows: 0,
  totalRows: 0,
  percentage: 0,
  stage: 'parsing',
  currentBatch: 0,
  totalBatches: 0,
  message: '准备处理...',
  validRows: 0,
  errorRows: 0,
  importedRows: 0
})

// 处理结果
const processResult = ref<any>(null)

// 方法
const handleClose = (): void => {
  // 如果正在下载或处理，取消任务
  if (downloading.value) {
    window.electron.ipcRenderer.invoke('excel:cancelProcess')
  }

  visible.value = false
  // 重置状态
  nextTick(() => {
    currentStep.value = 0
    downloading.value = false
    processing.value = false
    showErrorDetails.value = false
    urlForm.value.url = ''
    downloadProgress.value = {
      stage: 'waiting',
      message: '准备下载...',
      percentage: 0
    }
    processProgress.value = {
      processedRows: 0,
      totalRows: 0,
      percentage: 0,
      stage: 'parsing',
      currentBatch: 0,
      totalBatches: 0,
      message: '准备处理...',
      validRows: 0,
      errorRows: 0,
      importedRows: 0
    }
    processResult.value = null
  })
}

const handleStartDownload = async (): Promise<void> => {
  // 验证表单
  if (!urlFormRef.value) return

  const valid = await urlFormRef.value.validate().catch(() => false)
  if (!valid) return

  downloading.value = true
  currentStep.value = 1

  try {
    console.log('发送下载请求到主进程:', urlForm.value.url)

    // 发送开始下载请求
    window.electron.ipcRenderer.send('excel:downloadFromUrl', urlForm.value.url)
  } catch (error: any) {
    console.error('发送下载请求失败:', error)
    ElMessage.error(`发送下载请求失败：${error.message}`)
    downloading.value = false
    currentStep.value = 0
  }
}

// 事件处理函数
const handleDownloadStarted = (data: any): void => {
  console.log('下载开始:', data)
  downloadProgress.value.message = data.message || '开始下载...'
}

const handleDownloadProgress = (progress: any): void => {
  console.log('下载进度:', progress)
  downloadProgress.value = { ...downloadProgress.value, ...progress }
}

const handleDownloadComplete = (data: any): void => {
  console.log('下载完成:', data)
  downloading.value = false
  currentStep.value = 2

  // 开始处理Excel文件
  processing.value = true
  window.electron.ipcRenderer.send('excel:startProcess', data.filePath)
}

const handleDownloadError = (error: any): void => {
  console.error('下载失败:', error)
  downloading.value = false
  currentStep.value = 0
  ElMessage.error(`下载失败：${error.message}`)
}

const handleProcessStarted = (data: any): void => {
  console.log('处理开始:', data)
  processProgress.value.message = data.message || '开始处理...'
}

const handleProcessProgress = (progress: any): void => {
  console.log('处理进度:', progress)
  processProgress.value = { ...processProgress.value, ...progress }
}

const handleProcessComplete = (result: any): void => {
  console.log('处理完成:', result)
  processing.value = false
  currentStep.value = 3
  processResult.value = result

  // 通知父组件刷新数据
  emit('success')

  ElMessage.success(`导入完成！成功导入 ${result.successCount} 条记录`)
}

const handleProcessError = (error: any): void => {
  console.error('处理失败:', error)
  processing.value = false
  currentStep.value = 0
  ElMessage.error(`处理失败：${error.message}`)
}

// 组件挂载和卸载
onMounted(() => {
  // 监听下载相关事件
  window.electron.ipcRenderer.on('excel:downloadStarted', handleDownloadStarted)
  window.electron.ipcRenderer.on('excel:downloadProgress', handleDownloadProgress)
  window.electron.ipcRenderer.on('excel:downloadComplete', handleDownloadComplete)
  window.electron.ipcRenderer.on('excel:downloadError', handleDownloadError)

  // 监听处理相关事件
  window.electron.ipcRenderer.on('excel:processStarted', handleProcessStarted)
  window.electron.ipcRenderer.on('excel:processProgress', handleProcessProgress)
  window.electron.ipcRenderer.on('excel:processComplete', handleProcessComplete)
  window.electron.ipcRenderer.on('excel:processError', handleProcessError)
})

onUnmounted(() => {
  // 移除事件监听
  window.electron.ipcRenderer.removeAllListeners('excel:downloadStarted')
  window.electron.ipcRenderer.removeAllListeners('excel:downloadProgress')
  window.electron.ipcRenderer.removeAllListeners('excel:downloadComplete')
  window.electron.ipcRenderer.removeAllListeners('excel:downloadError')
  window.electron.ipcRenderer.removeAllListeners('excel:processStarted')
  window.electron.ipcRenderer.removeAllListeners('excel:processProgress')
  window.electron.ipcRenderer.removeAllListeners('excel:processComplete')
  window.electron.ipcRenderer.removeAllListeners('excel:processError')
})
</script>

<style scoped>
.auto-import-dialog {
  --el-dialog-border-radius: 12px;
}

.url-input-area {
  padding: 20px;
  background: var(--bs-gray-100);
  border-radius: 8px;
  margin-bottom: 20px;
}

[data-bs-theme='dark'] .url-input-area {
  background: var(--bs-body-bg);
}

.url-tips {
  margin-top: 15px;
}

.download-progress-area,
.process-progress-area,
.result-area {
  text-align: center;
  padding: 20px;
}

.progress-stats {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
}

.stat-label {
  font-size: 12px;
  color: var(--bs-secondary-color);
  margin-bottom: 5px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
}

.text-success {
  color: var(--bs-success);
}

.text-danger {
  color: var(--bs-danger);
}

.text-primary {
  color: var(--bs-primary);
}

.result-stats {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.stat-card {
  background: var(--bs-gray-100);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  min-width: 100px;
  border: 2px solid var(--bs-border-color);
}

[data-bs-theme='dark'] .stat-card {
  background: var(--bs-gray-800);
  border-color: var(--bs-border-color);
}

.stat-card.success {
  background: var(--bs-success-bg-subtle);
  border-color: var(--bs-success);
}

.stat-card.danger {
  background: var(--bs-danger-bg-subtle);
  border-color: var(--bs-danger);
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-card.success .stat-number {
  color: var(--bs-success);
}

.stat-card.danger .stat-number {
  color: var(--bs-danger);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
