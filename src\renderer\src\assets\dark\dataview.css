.dark .card-header-title {
  color: var(--title-text-color);
  font-size: var(--card-title-size);
  font-family: var(--card-title-family);
  font-weight: var(--card-title-weight);
}

.dark hr {
  background-color: #384869 !important;
  opacity: 30% !important;
  margin-left: 0px !important;
  margin-right: 0px !important;
}

/* 单独配置复盘分析的《配置列表项》样式 */
.dark .producttable .dropdown {
  position: relative;
  /* top: -30px; */
}

.dark .nav-link {
  color: var(--title-text-color) !important;
}

.dark .router-link-exact-active {
  background-color: #424348 !important;
}

.dark .nav-link:hover {
  background-color: #424348 !important;
}

.dark .card-info .card-info-title {
  color: var(--card-title-color) !important;
  font-size: 14px !important;
}

.dark .card-body .card-info-title span {
  color: var(--title-text-color);
  font-size: var(--card-title-size) !important;
  font-family: var(--card-title-family) !important;
  font-weight: var(--card-title-weight) !important;
}

.dark .card-body .card-info-index_name {
  color: var(--card-title-color) !important;
  font-size: 14px !important;
}

.dark .card-body h5 {
  color: var(--title-text-color);
  font-size: var(--card-title-size);
  font-family: var(--card-title-family);
  font-weight: var(--card-title-weight);
}

.dark .card-body-lineAreaChart {
  padding: 0 !important;
}

.dark .card-body-lineAreaChart .traffic-layout {
  padding: 16px 24px !important;
}

.dark .custom-legend-main {
  padding: 0 !important;
}

.dark .card-body .lineAreaChart .custom-legend-main-container .btn-chart:hover {
  background-color: var(--legend-bg-color) !important;
  color: var(--title-text-color) !important;
}

.dark .card-body .grid-container-dark {
  background-color: var(--popover-bg-color) !important;
}

.dark .card-body .grid-container-dark span {
  color: var(--text-color);
}

.dark .card-body .grid-item-container {
  border-bottom: 1px solid var(--popover-bg-color);
}

.dark .card-body .organic-traffic h3,
.dark .card-body .Paid-traffic h3 {
  color: var(--title-text-color);
  font-size: 14px;
}

.dark .card-body .metric-grid-container .metric-item .metric-content div {
  color: #fff !important;
}

.dark .card-body .metric-grid-container .metric-item {
  background-color: var(--legend-bg-color) !important;
}

.dark .card-body .dragable-table-container .header-actions {
  position: relative;
}

.dark .card-header .demo-page-wrapper div {
  border: 0 !important;
}

.dark .review-report .card-title,
.dark .review-report .card-title-p {
  color: var(--title-text-color);
  font-size: var(---title-text-size);
}

.dark .card-body .channel-table .el-table__row {
  background-color: var(--bg-color);
}

.dark .card-body .time-selector-area {
  background-color: var(--popover-bg-color) !important;
}

.dark .card-body .room-list-panel .card .card-header {
  background-color: var(--bg-color) !important;
}

.dark .card-body .card-body .form-label {
  color: var(--title-text-color);
}

.dark .card-body .quick-select-buttons {
  background-color: var(--bg-color) !important;
  border: 0;
}

.dark .card-body .time-selector-area .card {
  border: 0;
}

.dark .card-body .time-selector-area .card-header {
  border-bottom: 1px solid rgb(230, 230, 232, 0.1);
}

.dark .card-body .text-center-title {
  display: flex;
  flex-flow: column;
  align-items: start;
  justify-content: center;
  border-radius: 7.6px;
}

.dark .room-table td {
  border-bottom: 1px solid var(--button-dark-background-color) !important;
}

.dark .room-table thead {
  background-color: var(--popover-bg-color);
}

.dark .room-table thead th {
  border-bottom: 1px solid var(--button-dark-background-color) !important;
  color: #fff;
  font-weight: normal;
}

.dark .panelchar {
  color: #fff !important;
}

.dark .panlchar-container div {
  /* box-shadow: 0 0 0 !important; */
  border: 0px !important;
}

.dark .realtime-grid .realtime-metric-item div {
  background-color: #2c2c35 !important;
  color: #fff;
}

.dark .realtime-grid .realtime-metric-item .metric-header div {
  color: #8a8a98 !important;
  font-size: 12px;
}

.dark .aggregated-data-section .data-header {
  border-bottom: 1px solid rgb(56, 72, 105, 0.3);
}

/* 修改tooltip的边框 */
.dark .chart-box div:nth-child(2) {
  border: 0px !important;
}

/* 修改表格的样式 */
.dark .vxe-header--column,
.dark .vxe-table--header-inner-wrapper {
  background-color: #2c2c35 !important;
}

.dark .vxe-table--body .vxe-body--row {
  background-color: #282830 !important;
  color: #fff;
}

.dark .vxe-table--body .row--stripe td {
  background-color: #262631 !important;
  color: #fff;
}

.dark .vxe-table--body-inner-wrapper,
.dark .vxe-table--header-inner-wrapper {
  background-color: #212228 !important;
}

.dark .trend-divider {
  background-color: rgba(56, 72, 105, 0.3);
}

.dark .card-title-container p {
  color: #e0e6ff !important;
}

.dark .aggregated-data-section .control-buttons .time-span-selector {
  position: static;
}

.dark .card-title-style {
  display: flex;
  justify-content: normal;
  align-items: center;
}

.dark .first-metric-card .card-title-previous,
.dark .first-metric-card .card-title-current {
  color: #fff !important;
}

.dark .describe-style {
  font-size: 11px !important;
  color: #fff !important;
}

.dark .first-metric-card .card-title-current {
  font-size: 28px;
}

.dark .line--NNUEF {
  background: linear-gradient(
    180deg,
    rgba(172, 187, 255, 0),
    #acbbff 13.81%,
    #acbbff 78.73%,
    rgba(172, 187, 255, 0) 92.63%
  );
  height: 156px;
  margin-left: 32px;
  width: 1px;
}

.dark .first-card-title .card-title-describe {
  color: #fff !important;
}

.dark .first-card-title .card-title-current {
  color: #fff !important;
}

.dark .subtitlepanel-title,
.dark .videoplayer-title,
.dark .grid-title {
  color: #fff;
}

.dark .card-shadow-dark {
  border-radius: 60px;
}

.dark .dark-button-border {
  border-radius: 0 12px !important;
  background: hsla(0, 0%, 100%, 0.2) !important;
}

.dark .detail-report-content {
  color: #fff;
}

.dark .modal-content .modal-title,
.dark .modal-content .modal-body,
.dark .modal-content .modal-header {
  color: #000;
}

.dark .el-input-number__decrease,
.dark .el-input-number__increase {
  color: #000;
  background-color: transparent;
}
