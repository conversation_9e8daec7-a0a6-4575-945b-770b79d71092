/**
 * Excel导入服务
 * 负责Excel文件解析、数据验证和批量导入功能
 */
import { IpcMain } from 'electron'
import * as ExcelJS from 'exceljs'
import * as fs from 'fs'
import * as path from 'path'
import * as https from 'https'
import * as http from 'http'
import { createOrder } from './business'

// 字段映射配置
const fieldMapping = {
  // 必填字段
  订单id: 'order_id',
  商品id: 'product_id',
  商品名称: 'product_name',
  达人昵称: 'user_name',
  成交金额: 'total_pay_amount',
  订单状态: 'order_status',

  // 金额字段（元→分）
  总佣金收入: 'total_commission',
  '预估佣金收入-达人': 'estimated_user_comission',
  '预估佣金收入-机构': 'estimated_inst_comission',
  '结算佣金收入-达人': 'settle_user_comission',
  '结算佣金收入-机构': 'settle_inst_comission',
  预估技术服务费: 'platform_service_fee',
  结算技术服务费: 'settle_tech_service_fee',
  定金金额: 'stage_one_pay_money',
  达人预估奖励佣金收入: 'estimated_user_stepped_commission',
  机构预估奖励佣金收入: 'estimated_inst_stepped_commission',
  达人结算奖励佣金收入: 'settle_user_stepped_commission',
  机构结算奖励佣金收入: 'settle_inst_stepped_commission',

  // 百分比字段（%→整数×100）
  佣金率: 'commission_rate',
  分成比例: 'user_profit_ratio',
  冻结比例: 'frozen_rate',
  升佣佣金率: 'stepped_ratio',

  // 时间字段（时间字符串→Unix时间戳）
  订单支付时间: 'pay_time',
  订单收货时间: 'confirm_time',
  订单结算时间: 'settle_time',
  尾款支付时间: 'stage_two_pay_time',

  // 布尔字段（是/否→true/false）
  安心购: 'buy_at_ease',
  佣金发票: 'commission_invoice',
  是否阶梯佣金: 'is_stepped_plan',

  // 其他字段
  超时未结算原因: 'unsettled_event',
  新老账户: 'account_type',
  店铺id: 'shop_id',
  店铺名称: 'shop_name',
  门槛销量: 'threshold_order_count',
  流量细分来源: 'traffic_source',
  流量来源: 'media_type_group_name',
  订单类型: 'content_type'
}

interface ParsedItem {
  rowIndex: number
  isValid: boolean
  errors: string[]
  [key: string]: any
}

interface ParseProgress {
  processedRows: number
  totalRows: number
  percentage: number
  validRows: number
  errorRows: number
  importedRows: number
  currentBatch: number
  totalBatches: number
  stage: 'parsing' | 'importing' | 'complete'
  message: string
}

export default class ExcelImportService {
  private static IMPORT_BATCH_SIZE = 2000 // 每批导入500条
  private static isCancelled = false // 取消标识
  private static currentTempFilePath: string | null = null // 当前临时文件路径

  public static hook(ipcMain: IpcMain): void {
    // 流式处理Excel文件（解析+导入一体化）
    ipcMain.on('excel:startProcess', async (event, filePath: string) => {
      try {
        console.log('开始流式处理Excel文件:', filePath)

        // 重置取消标识
        this.isCancelled = false

        // 异步处理，不阻塞
        this.processExcelFileStream(filePath, (progress) => {
          // 发送进度更新到前端
          console.log('Excel流式处理进度:', progress)
          event.reply('excel:processProgress', progress)
        })
          .then((result) => {
            // 处理完成，发送结果到前端
            if (result.cancelled) {
              console.log('Excel流式处理已取消')
              event.reply('excel:processError', { message: '处理已取消' })
            } else {
              console.log('Excel流式处理完成:', {
                totalRows: result.totalRows,
                successCount: result.successCount,
                errorCount: result.errorCount
              })
              event.reply('excel:processComplete', result)
            }
          })
          .catch((error) => {
            // 处理失败，发送错误到前端
            console.error('Excel流式处理失败:', error)
            event.reply('excel:processError', {
              message: error.message || '处理失败'
            })
          })
          .finally(() => {
            // 重置取消标识
            this.isCancelled = false

            // 清理临时文件
            if (this.currentTempFilePath) {
              console.log('处理完成，清理临时文件:', this.currentTempFilePath)
              this.cleanupTempFile(this.currentTempFilePath)
              this.currentTempFilePath = null
            }
          })

        // 立即返回，表示开始处理
        event.reply('excel:processStarted', { message: '开始处理...' })
      } catch (error: any) {
        console.error('启动Excel流式处理失败:', error)
        event.reply('excel:processError', {
          message: error.message || '启动处理失败'
        })
      }
    })

    // 取消处理任务
    ipcMain.handle('excel:cancelProcess', async () => {
      console.log('收到取消处理请求')
      this.isCancelled = true
      return { success: true }
    })

    // URL下载Excel文件
    ipcMain.on('excel:downloadFromUrl', async (event, url: string) => {
      try {
        console.log('开始从URL下载Excel文件:', url)

        // 重置取消标识
        this.isCancelled = false

        // 异步下载，不阻塞
        this.downloadExcelFromUrl(url, (progress) => {
          // 发送下载进度更新到前端
          console.log('Excel下载进度:', progress)
          event.reply('excel:downloadProgress', progress)
        })
          .then((filePath) => {
            if (this.checkCancelled()) {
              console.log('Excel下载已取消')
              this.cleanupTempFile(filePath)
              event.reply('excel:downloadError', { message: '下载已取消' })
            } else {
              console.log('Excel下载完成，文件路径:', filePath)
              // 保存临时文件路径，用于后续清理
              this.currentTempFilePath = filePath
              console.log('发送下载完成事件到前端，数据:', { filePath })
              event.reply('excel:downloadComplete', { filePath })
            }
          })
          .catch((error) => {
            console.error('Excel下载失败:', error)
            console.error('错误详情:', error.stack)
            // 清理可能存在的临时文件
            if (this.currentTempFilePath) {
              this.cleanupTempFile(this.currentTempFilePath)
              this.currentTempFilePath = null
            }
            event.reply('excel:downloadError', {
              message: error.message || '下载失败'
            })
          })

        // 立即返回，表示开始下载
        event.reply('excel:downloadStarted', { message: '开始下载...' })
      } catch (error: any) {
        console.error('启动Excel下载失败:', error)
        event.reply('excel:downloadError', {
          message: error.message || '启动下载失败'
        })
      }
    })
  }

  /**
   * 检查是否已取消
   */
  private static checkCancelled(): boolean {
    return this.isCancelled
  }

  /**
   * 从URL下载Excel文件
   */
  private static async downloadExcelFromUrl(
    url: string,
    progressCallback: (progress: any) => void
  ): Promise<string> {
    console.log('开始下载Excel文件，URL:', url)
    return new Promise((resolve, reject) => {
      try {
        // 验证URL格式
        const urlObj = new URL(url)
        if (!['http:', 'https:'].includes(urlObj.protocol)) {
          throw new Error('仅支持HTTP和HTTPS协议')
        }
        console.log('URL验证通过:', urlObj.href)

        // 创建临时目录并清理旧文件
        const tempDir = path.join(process.cwd(), 'temp')
        if (!fs.existsSync(tempDir)) {
          fs.mkdirSync(tempDir, { recursive: true })
        }
        this.cleanupOldTempFiles()

        // 生成临时文件名
        const fileName = `excel_${Date.now()}.xlsx`
        const filePath = path.join(tempDir, fileName)
        console.log('生成临时文件路径:', filePath)

        progressCallback({
          stage: 'downloading',
          message: '正在连接服务器...',
          percentage: 0
        })

        // 选择http或https模块
        const httpModule = urlObj.protocol === 'https:' ? https : http

        const request = httpModule.get(url, (response) => {
          console.log('收到HTTP响应，状态码:', response.statusCode)
          // 检查响应状态
          if (response.statusCode !== 200) {
            console.error('HTTP响应状态码错误:', response.statusCode)
            reject(new Error(`下载失败: HTTP ${response.statusCode}`))
            return
          }

          // 获取文件大小
          const totalSize = parseInt(response.headers['content-length'] || '0', 10)
          let downloadedSize = 0

          // 创建文件写入流
          const fileStream = fs.createWriteStream(filePath)

          response.on('data', (chunk) => {
            if (this.checkCancelled()) {
              fileStream.destroy()
              this.cleanupTempFile(filePath)
              reject(new Error('下载已取消'))
              return
            }

            downloadedSize += chunk.length
            const percentage = totalSize > 0 ? Math.round((downloadedSize / totalSize) * 100) : 0

            progressCallback({
              stage: 'downloading',
              message: `正在下载... ${this.formatBytes(downloadedSize)}${totalSize > 0 ? ` / ${this.formatBytes(totalSize)}` : ''}`,
              percentage
            })
          })

          response.pipe(fileStream)

          fileStream.on('finish', () => {
            fileStream.close()
            console.log('文件写入完成:', filePath)

            // 检查文件是否存在
            if (!fs.existsSync(filePath)) {
              reject(new Error('文件下载完成但文件不存在'))
              return
            }

            // 检查文件大小
            const stats = fs.statSync(filePath)
            console.log('下载文件大小:', stats.size, 'bytes')
            if (stats.size === 0) {
              this.cleanupTempFile(filePath)
              reject(new Error('下载的文件为空'))
              return
            }

            // 验证文件是否为Excel格式
            console.log('开始验证Excel文件:', filePath)
            this.validateExcelFile(filePath)
              .then(() => {
                console.log('Excel文件验证成功，准备返回文件路径:', filePath)
                progressCallback({
                  stage: 'complete',
                  message: '下载完成',
                  percentage: 100
                })
                resolve(filePath)
              })
              .catch((error) => {
                // 删除无效文件
                console.error('Excel文件验证失败:', error)
                this.cleanupTempFile(filePath)
                reject(error)
              })
          })

          fileStream.on('error', (error) => {
            this.cleanupTempFile(filePath)
            reject(new Error(`文件写入失败: ${error.message}`))
          })
        })

        request.on('error', (error) => {
          reject(new Error(`网络请求失败: ${error.message}`))
        })

        // 设置超时
        request.setTimeout(30000, () => {
          request.destroy()
          reject(new Error('下载超时'))
        })
      } catch (error: any) {
        reject(new Error(`URL解析失败: ${error.message}`))
      }
    })
  }

  /**
   * 验证Excel文件格式
   */
  private static async validateExcelFile(filePath: string): Promise<void> {
    try {
      const workbook = new ExcelJS.Workbook()
      await workbook.xlsx.readFile(filePath)

      // 检查是否有工作表
      if (workbook.worksheets.length === 0) {
        throw new Error('Excel文件中没有工作表')
      }

      console.log('Excel文件验证通过')
    } catch (error: any) {
      throw new Error(`文件格式验证失败: ${error.message}`)
    }
  }

  /**
   * 格式化字节大小
   */
  private static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 清理临时文件
   */
  private static cleanupTempFile(filePath: string): void {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath)
        console.log('临时文件已清理:', filePath)
      }
    } catch (error) {
      console.warn('清理临时文件失败:', error)
    }
  }

  /**
   * 清理临时目录中的旧文件（超过1小时的文件）
   */
  private static cleanupOldTempFiles(): void {
    try {
      const tempDir = path.join(process.cwd(), 'temp')
      if (!fs.existsSync(tempDir)) return

      const files = fs.readdirSync(tempDir)
      const now = Date.now()
      const oneHour = 60 * 60 * 1000

      files.forEach((file) => {
        if (file.startsWith('excel_')) {
          const filePath = path.join(tempDir, file)
          const stats = fs.statSync(filePath)
          if (now - stats.mtime.getTime() > oneHour) {
            this.cleanupTempFile(filePath)
          }
        }
      })
    } catch (error) {
      console.warn('清理旧临时文件失败:', error)
    }
  }

  /**
   * 流式处理Excel文件（解析+导入一体化）
   */
  private static async processExcelFileStream(
    filePath: string,
    progressCallback: (progress: ParseProgress) => void
  ): Promise<any> {
    console.log('开始预处理模式的Excel流式处理:', filePath)

    // 第一步：快速统计总行数（解析阶段）
    console.log('第一步：快速统计总行数...')
    progressCallback({
      processedRows: 0,
      totalRows: 0,
      percentage: 0,
      validRows: 0,
      errorRows: 0,
      importedRows: 0,
      currentBatch: 0,
      totalBatches: 0,
      stage: 'parsing',
      message: '正在解析文件并统计总行数...'
    })

    const totalRows = await this.countTotalRows(filePath, progressCallback)

    // 检查是否被取消
    if (this.checkCancelled()) {
      return { cancelled: true }
    }

    console.log(`文件总行数统计完成: ${totalRows} 行`)

    // 第二步：实际流式处理（导入阶段）
    console.log('第二步：开始实际数据处理...')
    return await this.actualProcessData(filePath, totalRows, progressCallback)
  }

  /**
   * 第一步：快速统计总行数
   */
  private static async countTotalRows(
    filePath: string,
    progressCallback: (progress: ParseProgress) => void
  ): Promise<number> {
    const workbookReader = new ExcelJS.stream.xlsx.WorkbookReader(filePath, {})
    let totalRows = 0
    let isFirstWorksheet = true
    let isHeaderRow = true

    try {
      for await (const worksheetReader of workbookReader) {
        if (!isFirstWorksheet) continue
        isFirstWorksheet = false

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        for await (const _row of worksheetReader) {
          // 检查是否被取消
          if (this.checkCancelled()) {
            console.log('统计行数时检测到取消请求')
            throw new Error('操作已取消')
          }

          if (isHeaderRow) {
            isHeaderRow = false
            continue // 跳过表头行
          }
          totalRows++

          // 每处理1000行更新一次进度
          if (totalRows % 1000 === 0) {
            progressCallback({
              processedRows: 0,
              totalRows: totalRows,
              percentage: 0,
              validRows: 0,
              errorRows: 0,
              importedRows: 0,
              currentBatch: 0,
              totalBatches: 0,
              stage: 'parsing',
              message: `正在解析文件... 已发现 ${totalRows} 行数据`
            })
          }
        }
        break // 只处理第一个工作表
      }
    } catch (error) {
      console.error('统计行数时出错:', error)
      throw error
    }

    return totalRows
  }

  /**
   * 第二步：实际数据处理
   */
  private static async actualProcessData(
    filePath: string,
    totalRows: number,
    progressCallback: (progress: ParseProgress) => void
  ): Promise<any> {
    const workbookReader = new ExcelJS.stream.xlsx.WorkbookReader(filePath, {})

    let processedRows = 0
    let validRows = 0
    let errorRows = 0
    let importedRows = 0
    let currentBatch = 0
    let headers: string[] = []
    let isFirstWorksheet = true
    let isHeaderRow = true

    const sampleErrors: ParsedItem[] = []
    const batchData: any[] = []
    const totalBatches = Math.ceil(totalRows / this.IMPORT_BATCH_SIZE)

    console.log(`开始处理 ${totalRows} 行数据，预计 ${totalBatches} 个批次`)

    progressCallback({
      processedRows: 0,
      totalRows,
      percentage: 10, // 解析完成占10%
      validRows: 0,
      errorRows: 0,
      importedRows: 0,
      currentBatch: 0,
      totalBatches,
      stage: 'importing',
      message: '开始导入数据...'
    })

    try {
      for await (const worksheetReader of workbookReader) {
        if (!isFirstWorksheet) continue
        isFirstWorksheet = false

        let rowIndex = 0

        for await (const row of worksheetReader) {
          // 检查是否被取消
          if (this.checkCancelled()) {
            console.log('数据处理时检测到取消请求')
            return { cancelled: true }
          }

          rowIndex++

          // 处理row.values，确保是数组格式
          let rowValues: any[] = []
          if (row.values && Array.isArray(row.values)) {
            rowValues = row.values.slice(1)
          } else if (row.values && typeof row.values === 'object') {
            rowValues = Object.values(row.values)
          }

          // 第一行作为表头
          if (isHeaderRow) {
            headers = rowValues.map((v) => v?.toString() || '')
            console.log('表头:', headers)
            isHeaderRow = false
            continue
          }

          processedRows++

          // 处理当前行数据
          try {
            const processedItem = this.processRowData(rowValues, headers, rowIndex)

            if (processedItem.isValid) {
              validRows++
              const cleanItem = this.cleanItemForImport(processedItem)
              batchData.push(cleanItem)
            } else {
              errorRows++
              if (sampleErrors.length < 100) {
                sampleErrors.push(processedItem)
              }
            }

            // 批量导入处理
            if (batchData.length >= this.IMPORT_BATCH_SIZE) {
              currentBatch++

              // 更新进度 - 导入阶段
              const progress = 10 + Math.round((processedRows / totalRows) * 85) // 10%解析 + 85%导入
              progressCallback({
                processedRows,
                totalRows,
                percentage: progress,
                validRows,
                errorRows,
                importedRows,
                currentBatch,
                totalBatches,
                stage: 'importing',
                message: `导入第 ${currentBatch}/${totalBatches} 批数据... (${batchData.length}条)`
              })

              try {
                const importResult = await createOrder(batchData)
                importedRows += importResult.successCount || 0
                console.log(
                  `批次 ${currentBatch} 导入完成: ${importResult.successCount}/${batchData.length}`
                )
              } catch (error) {
                console.error(`批次 ${currentBatch} 导入失败:`, error)
              }

              // 清空批次数据
              batchData.length = 0
            }
          } catch (error) {
            console.error(`处理第${rowIndex}行时出错:`, error)
            errorRows++
          }

          // 让事件循环有机会处理其他任务
          if (processedRows % 50 === 0) {
            await new Promise((resolve) => setTimeout(resolve, 1))
          }
        }

        // 处理最后一批数据
        if (batchData.length > 0) {
          // 检查是否被取消
          if (this.checkCancelled()) {
            console.log('最后批次处理时检测到取消请求')
            return { cancelled: true }
          }

          currentBatch++

          progressCallback({
            processedRows,
            totalRows,
            percentage: 95,
            validRows,
            errorRows,
            importedRows,
            currentBatch,
            totalBatches: currentBatch,
            stage: 'importing',
            message: `导入最后一批数据... (${batchData.length}条)`
          })

          try {
            const importResult = await createOrder(batchData)
            importedRows += importResult.successCount || 0
            console.log(`最后批次导入完成: ${importResult.successCount}/${batchData.length}`)
          } catch (error) {
            console.error('最后批次导入失败:', error)
          }
        }

        break // 只处理第一个工作表
      }
    } catch (error) {
      console.error('实际处理过程中出错:', error)
      throw error
    }

    // 完成处理
    const finalResult = {
      totalRows,
      processedRows,
      validRows,
      errorRows,
      successCount: importedRows,
      failedCount: validRows - importedRows,
      sampleErrors: sampleErrors.slice(0, 100)
    }

    progressCallback({
      processedRows: totalRows,
      totalRows,
      percentage: 100,
      validRows,
      errorRows,
      importedRows,
      currentBatch,
      totalBatches: currentBatch,
      stage: 'complete',
      message: `导入完成！成功导入 ${importedRows}/${validRows} 条有效记录`
    })

    console.log('流式处理完成:', finalResult)
    return finalResult
  }

  /**
   * 处理单行数据
   */
  private static processRowData(row: any[], headers: string[], rowIndex: number): ParsedItem {
    const item: any = { rowIndex: rowIndex + 2 } // Excel行号从2开始（第1行是表头）
    const errors: string[] = []

    // 字段映射和验证
    headers.forEach((header, colIndex) => {
      const fieldName = fieldMapping[header as keyof typeof fieldMapping]
      if (fieldName && row[colIndex] !== undefined && row[colIndex] !== null) {
        let value = row[colIndex]

        // 通用字符串清理
        if (typeof value === 'string') {
          value = value.trim()
        }

        // 数据类型转换和验证
        switch (fieldName) {
          case 'total_pay_amount':
          case 'total_commission':
          case 'estimated_user_comission':
          case 'estimated_inst_comission':
          case 'settle_user_comission':
          case 'settle_inst_comission':
          case 'platform_service_fee':
          case 'settle_tech_service_fee':
          case 'stage_one_pay_money':
          case 'estimated_user_stepped_commission':
          case 'estimated_inst_stepped_commission':
          case 'settle_user_stepped_commission':
          case 'settle_inst_stepped_commission':
          case 'real_comission':
          case 'estimated_comission':
            // 金额转换为分
            if (typeof value === 'number') {
              value = isNaN(value) ? null : Math.round(value * 100)
            } else if (typeof value === 'string' && value.trim() !== '' && value !== '-') {
              const numValue = parseFloat(value.replace(/[^\d.]/g, ''))
              value = isNaN(numValue) ? null : Math.round(numValue * 100)
            } else {
              value = null
            }

            // 必填字段验证
            if (fieldName === 'total_pay_amount' && (value === null || value <= 0)) {
              errors.push('成交金额格式错误或为0')
            }
            break
          case 'commission_rate':
          case 'stepped_ratio':
          case 'user_profit_ratio':
          case 'frozen_rate':
            // 百分比转换为整数（如50.00% -> 5000）
            if (typeof value === 'number') {
              if (isNaN(value)) {
                value = null
              } else {
                // 如果是小数（如0.5表示50%），乘以10000
                value = value <= 1 ? Math.round(value * 10000) : Math.round(value * 100)
              }
            } else if (typeof value === 'string' && value.trim() !== '' && value !== '-') {
              // 去除%号后转换
              const numValue = parseFloat(value.replace('%', '').trim())
              value = isNaN(numValue) ? null : Math.round(numValue * 100)
            } else {
              value = fieldName === 'user_profit_ratio' ? 10000 : null
            }
            break
          case 'pay_time':
          case 'confirm_time':
          case 'settle_time':
          case 'stage_two_pay_time':
            // 时间转换为时间戳
            if (value && typeof value === 'string') {
              // 处理"-"表示空值的情况
              if (value === '-') {
                value = null
              } else {
                const timestamp = new Date(value).getTime() / 1000
                value = isNaN(timestamp) ? null : timestamp
                if (value === null) {
                  errors.push(`${header}格式错误`)
                }
              }
            } else if (typeof value === 'number') {
              // Excel日期序列号转换
              const excelDate = new Date((value - 25569) * 86400 * 1000)
              value = Math.floor(excelDate.getTime() / 1000)
            } else if (value instanceof Date) {
              // ExcelJS 返回Date对象的情况
              value = Math.floor(value.getTime() / 1000)
            }
            break
          case 'buy_at_ease':
          case 'commission_invoice':
          case 'is_stepped_plan':
            // 布尔值转换
            if (typeof value === 'string') {
              value = value === '是' || value === 'true' || value === '1'
            } else {
              value = Boolean(value)
            }
            break
          case 'order_status':
            // 订单状态映射
            if (typeof value === 'string') {
              const statusMap: { [key: string]: string } = {
                订单付款: 'PAY_SUCC',
                订单收货: 'CONFIRM',
                订单退货退款: 'REFUND',
                订单结算: 'SETTLE'
              }
              value = statusMap[value] || value
            }
            break
          case 'threshold_order_count':
            // 整数字段
            if (typeof value === 'number') {
              value = isNaN(value) ? null : Math.round(value)
            } else if (typeof value === 'string' && value.trim() !== '' && value !== '-') {
              const numValue = parseInt(value)
              value = isNaN(numValue) ? null : numValue
            } else {
              value = null
            }
            break
          case 'shop_id':
          case 'order_id':
          case 'product_id':
            // 字符串字段，去除制表符等特殊字符
            if (typeof value === 'string') {
              value = value.replace(/[\t\r\n]/g, '').trim()
            } else if (typeof value === 'number') {
              value = value.toString()
            }
            break
        }

        item[fieldName] = value
      }
    })

    // 必填字段验证
    const requiredFields = [
      'order_id',
      'product_id',
      'product_name',
      'user_name',
      'total_pay_amount',
      'order_status'
    ]

    const fieldDisplayNames: { [key: string]: string } = {
      order_id: '订单ID',
      product_id: '商品ID',
      product_name: '商品名称',
      user_name: '达人昵称',
      total_pay_amount: '成交金额',
      order_status: '订单状态'
    }

    requiredFields.forEach((field) => {
      if (!item[field] || (typeof item[field] === 'string' && item[field].trim() === '')) {
        errors.push(`${fieldDisplayNames[field] || field}不能为空`)
      }
    })

    // 业务规则验证
    if (item.order_id && typeof item.order_id === 'string' && item.order_id.length < 3) {
      errors.push('订单ID长度至少3位')
    }

    item.isValid = errors.length === 0
    item.errors = errors

    return item as ParsedItem
  }

  /**
   * 清理数据项用于导入
   */
  private static cleanItemForImport(item: ParsedItem): any {
    // 使用对象解构，排除不需要的字段
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { rowIndex: _rowIndex, isValid: _isValid, errors: _errors, ...cleanItem } = item

    // 清理NaN值和undefined
    Object.keys(cleanItem).forEach((key) => {
      if (
        cleanItem[key] === undefined ||
        (typeof cleanItem[key] === 'number' && isNaN(cleanItem[key]))
      ) {
        cleanItem[key] = null
      }
    })

    return cleanItem
  }
}
