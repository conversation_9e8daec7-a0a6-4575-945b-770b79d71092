<template>
  <el-dialog
    v-model="visible"
    title="导入Excel数据"
    width="1000px"
    top="5vh"
    destroy-on-close
    class="excel-import-dialog"
    @close="handleClose"
  >
    <!-- 步骤指示器 -->
    <el-steps :active="currentStep" align-center style="margin-bottom: 30px">
      <el-step title="选择文件" />
      <el-step title="处理进度" />
    </el-steps>

    <!-- 步骤 1: 文件选择 -->
    <div v-if="currentStep === 0">
      <!-- 文件选择区域 -->
      <div class="file-select-area">
        <el-button
          type="primary"
          size="large"
          :loading="selectingFile"
          style="width: 100%; height: 120px"
          @click="selectExcelFile"
        >
          <div style="display: flex; flex-direction: column; align-items: center">
            <el-icon style="font-size: 48px; margin-bottom: 10px">
              <i class="bi bi-file-earmark-excel file-select-area-icon" style="font-size: 48px"></i>
            </el-icon>
            <span style="font-size: 16px">
              {{ selectedFilePath ? '重新选择Excel文件' : '选择Excel文件' }}
            </span>
          </div>
        </el-button>

        <!-- 已选择文件显示 -->
        <div
          v-if="selectedFilePath"
          style="
            margin-top: 15px;
            padding: 15px;
            background-color: #f0f9ff;
            border-radius: 6px;
            border: 1px solid #b3d8ff;
          "
        >
          <div style="display: flex; align-items: center; justify-content: space-between">
            <div style="display: flex; align-items: center">
              <el-icon style="margin-right: 8px; color: #409eff">
                <i class="bi bi-file-earmark-excel"></i>
              </el-icon>
              <div>
                <div style="font-weight: 500; color: #333">{{ getFileName(selectedFilePath) }}</div>
                <div style="font-size: 12px; color: #666; margin-top: 2px">
                  {{ selectedFilePath }}
                </div>
              </div>
            </div>
            <el-button type="text" style="color: #f56c6c" @click="clearSelectedFile">
              清除
            </el-button>
          </div>
        </div>

        <!-- 提示信息 -->
        <div style="margin-top: 15px; color: #666; font-size: 14px; text-align: center">
          支持 .xlsx/.xls 格式文件，支持大数据量流式处理，自动解析并导入数据
        </div>
      </div>

      <!-- 字段映射说明 -->
      <el-card style="margin-top: 20px" shadow="never">
        <template #header>
          <div class="card-header">
            <span>Excel字段映射说明</span>
          </div>
        </template>
        <el-table :data="fieldMappingData" size="small" border max-height="300">
          <el-table-column prop="excelColumn" label="Excel列名" width="180" show-overflow-tooltip />
          <el-table-column prop="fieldName" label="对应字段" width="180" show-overflow-tooltip />
          <el-table-column prop="required" label="是否必填" width="100">
            <template #default="{ row }">
              <el-tag :type="row.required ? 'danger' : 'info'" size="small">
                {{ row.required ? '必填' : '可选' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="说明" show-overflow-tooltip />
        </el-table>
      </el-card>
    </div>

    <!-- 步骤 2: 处理进度和结果 -->
    <div v-if="currentStep === 1" class="process-container">
      <!-- 处理进度 -->
      <div v-if="processing" style="margin-bottom: 30px">
        <!-- 整体进度条 -->
        <div style="margin-bottom: 20px">
          <el-progress
            :percentage="processProgress.percentage"
            :status="processProgress.percentage === 100 ? 'success' : 'active'"
            :stroke-width="8"
          >
            <template #default="{ percentage }">
              <span class="percentage-value">{{ percentage }}%</span>
            </template>
          </el-progress>
          <div style="text-align: center; margin-top: 10px">
            <div style="font-weight: 500; color: #333; margin-bottom: 5px">
              {{ processProgress.message }}
            </div>
            <div style="color: #666; font-size: 14px">
              处理阶段: {{ getStageText(processProgress.stage) }} | 批次:
              {{ processProgress.currentBatch }}/{{ processProgress.totalBatches }}
            </div>
          </div>
        </div>

        <!-- 详细统计 -->
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic
              title="已处理"
              :value="processProgress.processedRows"
              :suffix="`/${processProgress.totalRows}`"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic title="有效记录" :value="processProgress.validRows" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="已导入" :value="processProgress.importedRows" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="错误记录" :value="processProgress.errorRows" />
          </el-col>
        </el-row>
      </div>

      <!-- 处理完成结果 -->
      <div v-if="!processing && processResult" style="text-align: center; padding: 40px">
        <el-result
          :icon="processResult.successCount > 0 ? 'success' : 'error'"
          :title="processResult.successCount > 0 ? '处理完成' : '处理失败'"
          :sub-title="getResultMessage()"
        >
          <template #extra>
            <el-row :gutter="20" style="margin-bottom: 20px">
              <el-col :span="8">
                <el-statistic title="总记录数" :value="processResult.totalRows" />
              </el-col>
              <el-col :span="8">
                <el-statistic title="成功导入" :value="processResult.successCount" />
              </el-col>
              <el-col :span="8">
                <el-statistic title="错误记录" :value="processResult.errorRows" />
              </el-col>
            </el-row>

            <!-- 错误详情 -->
            <div v-if="processResult.sampleErrors && processResult.sampleErrors.length > 0">
              <el-button type="text" @click="showErrorDetails = !showErrorDetails">
                {{ showErrorDetails ? '隐藏' : '查看' }}错误详情
              </el-button>
              <el-collapse v-if="showErrorDetails" style="margin-top: 15px; text-align: left">
                <el-collapse-item title="错误记录详情" name="errors">
                  <el-table :data="processResult.sampleErrors" size="small" max-height="200" border>
                    <el-table-column prop="rowIndex" label="行号" width="80" />
                    <el-table-column prop="errors" label="错误信息">
                      <template #default="{ row }">
                        <el-tag
                          v-for="error in row.errors"
                          :key="error"
                          type="danger"
                          size="small"
                          style="margin-right: 5px"
                        >
                          {{ error }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-collapse-item>
              </el-collapse>
            </div>

            <div style="margin-top: 20px">
              <el-button type="primary" @click="handleClose">完成</el-button>
            </div>
          </template>
        </el-result>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-if="currentStep === 0"
          type="primary"
          :disabled="!selectedFilePath"
          :loading="processing"
          @click="handleProcessFile"
        >
          {{ processing ? '处理中...' : '开始处理' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const currentStep = ref(0)
const selectedFilePath = ref('')
const selectingFile = ref(false)
const processing = ref(false)
const showErrorDetails = ref(false)

// 处理进度相关
const processProgress = ref({
  processedRows: 0,
  totalRows: 0,
  percentage: 0,
  stage: 'parsing', // 处理阶段: parsing, importing, complete
  currentBatch: 0,
  totalBatches: 0,
  message: '请选择文件开始处理',
  validRows: 0,
  errorRows: 0,
  importedRows: 0
})

// 处理结果
const processResult = ref<any>(null)

// 字段映射配置（显示用）
const fieldMappingData = [
  // 必填字段
  { excelColumn: '订单id', fieldName: 'order_id', required: true, description: '订单的唯一标识' },
  { excelColumn: '商品id', fieldName: 'product_id', required: true, description: '商品的唯一标识' },
  { excelColumn: '商品名称', fieldName: 'product_name', required: true, description: '商品的名称' },
  {
    excelColumn: '达人昵称',
    fieldName: 'user_name',
    required: true,
    description: '推广达人的昵称'
  },
  {
    excelColumn: '成交金额',
    fieldName: 'total_pay_amount',
    required: true,
    description: '订单金额，Excel单位：元，存储单位：分'
  },
  {
    excelColumn: '订单状态',
    fieldName: 'order_status',
    required: true,
    description: '如：订单付款→PAY_SUCC、已结算→SETTLED等'
  },

  // 可选字段 - 金额类（元→分）
  {
    excelColumn: '总佣金收入',
    fieldName: 'total_commission',
    required: false,
    description: '总佣金金额，Excel单位：元，存储单位：分'
  },
  {
    excelColumn: '预估佣金收入-达人',
    fieldName: 'estimated_user_comission',
    required: false,
    description: '预估用户佣金收入，Excel单位：元，存储单位：分'
  },
  {
    excelColumn: '预估佣金收入-机构',
    fieldName: 'estimated_inst_comission',
    required: false,
    description: '预估机构佣金收入，Excel单位：元，存储单位：分'
  },
  {
    excelColumn: '结算佣金收入-达人',
    fieldName: 'settle_user_comission',
    required: false,
    description: '结算用户佣金收入，Excel单位：元，存储单位：分'
  },
  {
    excelColumn: '结算佣金收入-机构',
    fieldName: 'settle_inst_comission',
    required: false,
    description: '结算机构佣金收入，Excel单位：元，存储单位：分'
  },
  {
    excelColumn: '预估技术服务费',
    fieldName: 'platform_service_fee',
    required: false,
    description: '预估技术服务费，Excel单位：元，存储单位：分'
  },
  {
    excelColumn: '结算技术服务费',
    fieldName: 'settle_tech_service_fee',
    required: false,
    description: '结算技术服务费，Excel单位：元，存储单位：分'
  },
  {
    excelColumn: '定金金额',
    fieldName: 'stage_one_pay_money',
    required: false,
    description: '第一期支付金额，Excel单位：元，存储单位：分'
  },
  {
    excelColumn: '达人预估奖励佣金收入',
    fieldName: 'estimated_user_stepped_commission',
    required: false,
    description: '用户奖励预估佣金，Excel单位：元，存储单位：分'
  },
  {
    excelColumn: '机构预估奖励佣金收入',
    fieldName: 'estimated_inst_stepped_commission',
    required: false,
    description: '机构奖励预估佣金，Excel单位：元，存储单位：分'
  },
  {
    excelColumn: '达人结算奖励佣金收入',
    fieldName: 'settle_user_stepped_commission',
    required: false,
    description: '用户奖励结算佣金，Excel单位：元，存储单位：分'
  },
  {
    excelColumn: '机构结算奖励佣金收入',
    fieldName: 'settle_inst_stepped_commission',
    required: false,
    description: '机构奖励结算佣金，Excel单位：元，存储单位：分'
  },

  // 可选字段 - 百分比类（%→整数×100）
  {
    excelColumn: '佣金率',
    fieldName: 'commission_rate',
    required: false,
    description: '佣金比例，Excel：50.00%，存储：5000（×100）'
  },
  {
    excelColumn: '分成比例',
    fieldName: 'user_profit_ratio',
    required: false,
    description: '用户分成比例，Excel：100.00%，存储：10000（×100）'
  },
  {
    excelColumn: '冻结比例',
    fieldName: 'frozen_rate',
    required: false,
    description: '佣金冻结比例，Excel：0%，存储：0（×100）'
  },
  {
    excelColumn: '升佣佣金率',
    fieldName: 'stepped_ratio',
    required: false,
    description: '阶梯佣金比例，Excel：%格式，存储：×100'
  },

  // 可选字段 - 时间类（时间字符串→Unix时间戳）
  {
    excelColumn: '订单支付时间',
    fieldName: 'pay_time',
    required: false,
    description: 'Excel格式：YYYY-MM-DD HH:mm:ss，存储：Unix时间戳'
  },
  {
    excelColumn: '订单收货时间',
    fieldName: 'confirm_time',
    required: false,
    description: 'Excel格式：YYYY-MM-DD HH:mm:ss，存储：Unix时间戳'
  },
  {
    excelColumn: '订单结算时间',
    fieldName: 'settle_time',
    required: false,
    description: 'Excel格式：YYYY-MM-DD HH:mm:ss，存储：Unix时间戳'
  },
  {
    excelColumn: '尾款支付时间',
    fieldName: 'stage_two_pay_time',
    required: false,
    description: '分期第二期支付时间'
  },

  // 可选字段 - 布尔类（是/否→true/false）
  {
    excelColumn: '安心购',
    fieldName: 'buy_at_ease',
    required: false,
    description: '是否为安心购商品，Excel：是/否，存储：true/false'
  },
  {
    excelColumn: '佣金发票',
    fieldName: 'commission_invoice',
    required: false,
    description: '是否开具佣金发票，Excel：是/否，存储：true/false'
  },
  {
    excelColumn: '是否阶梯佣金',
    fieldName: 'is_stepped_plan',
    required: false,
    description: '是否为阶梯佣金计划，Excel：是/否，存储：true/false'
  },

  // 可选字段 - 其他类型
  { excelColumn: '新老账户', fieldName: 'account_type', required: false, description: '账户类型' },
  { excelColumn: '店铺id', fieldName: 'shop_id', required: false, description: '店铺ID' },
  { excelColumn: '店铺名称', fieldName: 'shop_name', required: false, description: '店铺名称' },
  {
    excelColumn: '门槛销量',
    fieldName: 'threshold_order_count',
    required: false,
    description: '阶梯佣金门槛订单数'
  },
  {
    excelColumn: '流量细分来源',
    fieldName: 'traffic_source',
    required: false,
    description: '流量细分渠道'
  },
  {
    excelColumn: '流量来源',
    fieldName: 'media_type_group_name',
    required: false,
    description: '如：直播、橱窗等'
  },
  { excelColumn: '订单类型', fieldName: 'content_type', required: false, description: '内容类型' },
  {
    excelColumn: '超时未结算原因',
    fieldName: 'unsettled_event',
    required: false,
    description: '超时未结算原因'
  }
]

// 计算属性
const getStageText = (stage: string) => {
  switch (stage) {
    case 'parsing':
      return '解析中'
    case 'importing':
      return '导入中'
    case 'complete':
      return '完成'
    default:
      return '未知阶段'
  }
}

const getResultMessage = () => {
  if (processResult.value) {
    if (processResult.value.successCount > 0) {
      return `成功导入 ${processResult.value.successCount} 条记录${processResult.value.failedOrders?.length ? `，失败 ${processResult.value.failedOrders.length} 条` : ''}`
    } else {
      return processResult.value.message || '处理失败'
    }
  }
  return '请选择文件开始处理'
}

// 监听处理进度
const handleProcessProgress = (_: any, progress: any) => {
  console.log('处理进度:', progress)
  processProgress.value = progress
}

// 监听处理开始
const handleProcessStarted = (_: any, data: any) => {
  console.log('处理开始:', data.message)
  currentStep.value = 1
}

// 监听处理完成
const handleProcessComplete = (_: any, result: any) => {
  console.log('处理完成:', result)
  processResult.value = result
  processing.value = false

  ElMessage.success(`处理完成，共${result.totalRows}条记录，成功导入${result.successCount}条`)

  if (result.successCount > 0) {
    emit('success')
  }
}

// 监听处理错误
const handleProcessError = (_: any, error: any) => {
  console.error('处理错误:', error)
  processing.value = false
  ElMessage.error(`处理失败：${error.message}`)
}

// 文件选择相关方法
const selectExcelFile = async (): Promise<void> => {
  selectingFile.value = true
  try {
    const filePath = await window.electron.ipcRenderer.invoke('select-excel-file')
    if (filePath) {
      selectedFilePath.value = filePath
      ElMessage.success('文件选择成功')
    }
  } catch (error: any) {
    ElMessage.error(`文件选择失败：${error.message}`)
  } finally {
    selectingFile.value = false
  }
}

const clearSelectedFile = (): void => {
  selectedFilePath.value = ''
}

const getFileName = (filePath: string): string => {
  return filePath.split(/[/\\]/).pop() || ''
}

// 方法
const handleClose = (): void => {
  // 如果正在处理，取消处理任务
  if (processing.value) {
    window.electron.ipcRenderer.invoke('excel:cancelProcess')
  }

  visible.value = false
  // 重置状态
  nextTick(() => {
    currentStep.value = 0
    selectedFilePath.value = ''
    processing.value = false
    showErrorDetails.value = false
    processProgress.value = {
      processedRows: 0,
      totalRows: 0,
      percentage: 0,
      stage: 'parsing',
      currentBatch: 0,
      totalBatches: 0,
      message: '请选择文件开始处理',
      validRows: 0,
      errorRows: 0,
      importedRows: 0
    }
    processResult.value = null
  })
}

const handleProcessFile = async (): Promise<void> => {
  if (!selectedFilePath.value) {
    ElMessage.error('请先选择文件')
    return
  }

  processing.value = true
  processProgress.value = {
    processedRows: 0,
    totalRows: 0,
    percentage: 0,
    stage: 'parsing',
    currentBatch: 0,
    totalBatches: 0,
    message: '准备开始处理...',
    validRows: 0,
    errorRows: 0,
    importedRows: 0
  }

  try {
    console.log('发送处理请求到主进程:', selectedFilePath.value)

    // 发送开始处理请求，不等待返回
    window.electron.ipcRenderer.send('excel:startProcess', selectedFilePath.value)
  } catch (error: any) {
    console.error('发送处理请求失败:', error)
    ElMessage.error(`发送处理请求失败：${error.message}`)
    processing.value = false
  }
}

// 组件挂载和卸载
onMounted(() => {
  // 监听处理相关事件
  window.electron.ipcRenderer.on('excel:processProgress', handleProcessProgress)
  window.electron.ipcRenderer.on('excel:processStarted', handleProcessStarted)
  window.electron.ipcRenderer.on('excel:processComplete', handleProcessComplete)
  window.electron.ipcRenderer.on('excel:processError', handleProcessError)
})

onUnmounted(() => {
  // 移除事件监听
  window.electron.ipcRenderer.removeAllListeners('excel:processProgress')
  window.electron.ipcRenderer.removeAllListeners('excel:processStarted')
  window.electron.ipcRenderer.removeAllListeners('excel:processComplete')
  window.electron.ipcRenderer.removeAllListeners('excel:processError')
})
</script>

<style scoped>
.excel-import-dialog :deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
}

.excel-import-dialog :deep(.el-dialog) {
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
}

.file-select-area {
  margin-bottom: 20px;
}

.file-select-area-icon {
  color: #409eff;
}
.dark .file-select-area-icon {
  color: #fff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

.process-container {
  padding-left: 50px;
  padding-right: 50px;
  max-height: 65vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.percentage-value {
  font-weight: bold;
  margin-right: 10px;
}

.progress-text {
  color: #666;
  font-size: 12px;
}

.large-file-notice {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}
</style>
