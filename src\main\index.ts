import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import LZYService from './Service/LZYService'
import GFService from './Service/GFService'
import DemoService from './Service/DemoService'
import UpdateService from './Service/UpdateService'
import AIService from './Service/AIService'
import EnterService from './Service/EnterService'
import ExcelImportService from './Service/ExcelImportService'
import { session } from 'electron'
import path from 'path'
import SqliteDB from './module/SqliteDB'
// import remoteMain from '@electron/remote/main'
// remoteMain.initialize()
const vueDevToolsPath = path.resolve(__dirname, '../../extension/vue-devtools')
// 启用垃圾回收
app.commandLine.appendSwitch('js-flags', '--expose-gc')
// 禁用硬件加速
// app.disableHardwareAcceleration();

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false
    }
  })
  // 启用 remote 支持
  // remoteMain.enable(mainWindow.webContents)

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  // ipcMain.on('open-html-window', () => {
  //
  //   const printWindow = new BrowserWindow({
  //     show: true,
  //     webPreferences: {
  //       contextIsolation: true,
  //       sandbox: false
  //     }
  //   })

  //   printWindow.on('ready-to-show', () => {
  //     printWindow.show()
  //   })
  //   if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
  //     printWindow.loadURL(process.env['ELECTRON_RENDERER_URL'] + "/printcontent.html")
  //   } else {
  //     printWindow.loadFile(join(__dirname, '../renderer/printcontent.html'))
  //   }

  // })
  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')
  if (process.env.NODE_ENV === 'development') {
    await session.defaultSession.loadExtension(vueDevToolsPath)
  }
  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // 自动初始化数据库
  await SqliteDB.initializeDatabase()

  // 注册IPC事件处理程序
  GFService.hook(ipcMain)
  LZYService.hook(ipcMain)
  DemoService.hook(ipcMain)
  UpdateService.hook(ipcMain)
  AIService.hook(ipcMain) // 注册AI分析服务
  EnterService.hook(ipcMain) // 注册进入记录服务
  ExcelImportService.hook(ipcMain) // 注册Excel导入服务
  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
