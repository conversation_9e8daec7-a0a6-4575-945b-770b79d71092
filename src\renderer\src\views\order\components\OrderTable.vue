<template>
  <div class="h-100">
    <!-- 商品统计指标 -->
    <OrderMetrics :metrics="currentMetrics" />

    <!-- 数据表格 -->
    <div class="card" style="height: calc(100% - 195px)">
      <div class="card-body h-100">
        <DragableTable
          :columns="tableColumns"
          :data="orderList"
          storage-key="productAnalysisTable_config"
          :pagination="{
            enabled: true,
            remoteMode: true,
            pageSize: pageSize,
            currentPage: currentPage,
            total: totalOrders,
            pageSizes: [10, 15, 20, 30],
            showTotal: true,
            layout: 'total, sizes, prev, pager, next, jumper'
          }"
          :show-index="true"
          border
          stripe
          show-overflow
          :loading="isLoading"
          :show-header-config="false"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        >
          <template #toolbox>
            <!-- 搜索和筛选 -->
            <div class="row w-100 align-items-end">
              <div class="col-md-3">
                <el-autocomplete
                  v-model="searchQuery"
                  :fetch-suggestions="querySearchAsync"
                  placeholder="搜索商品名称..."
                  style="width: 100%"
                  @select="handleProductSelect"
                  @input="debouncedSearch"
                >
                  <template #default="{ item }">
                    <div>
                      <div style="font-weight: bold">{{ item.product_name }}</div>
                      <div style="font-size: 12px; color: #999">ID: {{ item.product_id }}</div>
                    </div>
                  </template>
                </el-autocomplete>
              </div>
              <div class="col-md-2">
                <el-select
                  v-model="statusFilter"
                  placeholder="全部状态"
                  style="width: 100%"
                  @change="handleFilter"
                >
                  <el-option label="全部状态" value="" />
                  <el-option label="支付成功" value="PAY_SUCC" />
                  <el-option label="退货退款" value="REFUND" />
                  <el-option label="已收货" value="CONFIRM" />
                  <el-option label="已结算" value="SETTLE" />
                </el-select>
              </div>
              <div class="col-md-2">
                <el-select
                  v-model="channelFilter"
                  placeholder="全部渠道"
                  style="width: 100%"
                  @change="handleChannelFilter"
                >
                  <el-option label="全部渠道" value="" />
                  <el-option label="直播" value="直播" />
                  <el-option label="橱窗" value="橱窗" />
                </el-select>
              </div>
              <div class="col-md-3 position-relative">
                <el-date-picker
                  v-model="dateTimeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  size="default"
                  style="width: 100%"
                  @change="handleDateTimeFilter"
                />
                <!-- 来源标识 -->
                <el-tooltip
                  v-if="startTime !== null && endTime !== null"
                  content="来源：直播分析页面的精确时间范围"
                  placement="top"
                >
                  <el-tag
                    type="info"
                    size="small"
                    class="position-absolute"
                    style="top: -8px; right: 8px; font-size: 10px; padding: 1px 4px"
                  >
                    直播
                  </el-tag>
                </el-tooltip>
              </div>

              <div class="col-md-2 d-flex align-items-end gap-2">
                <el-button
                  type="primary"
                  size="default"
                  title="导入Excel数据"
                  @click="showImportModal = true"
                >
                  <i class="bi bi-upload"></i>
                  导入数据
                </el-button>

                <el-button
                  type="success"
                  size="default"
                  title="从URL自动导入Excel数据"
                  @click="showAutoImportModal = true"
                >
                  <i class="bi bi-cloud-download"></i>
                  自动导入
                </el-button>

                <el-button
                  type="default"
                  size="default"
                  title="清除所有筛选条件"
                  @click="clearAllFilters"
                >
                  <i class="bi bi-x-circle"></i>
                  清除
                </el-button>
              </div>
            </div>
          </template>
          <!-- 订单ID -->
          <template #order_id="{ row }">
            <div class="order-item">
              <div class="order-id">{{ row.order_id }}</div>
              <span class="badge text-bg-primary">{{ formatOrderStatus(row.order_status) }}</span>
            </div>
          </template>
          <!-- 商品详情 -->
          <template #product_detail="{ row }">
            <div class="product-detail">
              <div class="product-name">{{ row.product_name }}</div>
              <div class="product-price small text-secondary">ID：{{ row.product_id }}</div>
            </div>
          </template>
          <!-- 分成比例 -->
          <template #user_profit_ratio="{ row }">
            <div class="user-profit-ratio">
              <span class="text-secondary">达人 </span>
              <span class="user-profit-ratio-value"
                >{{ (row.user_profit_ratio / 100).toFixed(2) }}%</span
              >
            </div>
          </template>
          <!-- 预估佣金收入 -->
          <template #estimated_comission="{ row }">
            <div class="estimated-comission">
              <span class="text-secondary">达人 </span>
              <span class="estimated-comission-value">{{
                formatAmount(row.estimated_user_comission)
              }}</span>
            </div>
            <div class="estimated-comission">
              <span class="text-secondary">机构 </span>
              <span class="estimated-comission-value">{{
                formatAmount(row.estimated_inst_comission)
              }}</span>
            </div>
          </template>
          <!-- 预估奖励佣金收入 -->
          <template #estimated_user_stepped_commission="{ row }">
            <div class="estimated-user-stepped-commission">
              <span class="text-secondary">达人 </span>
              <span class="estimated-user-stepped-commission-value">{{
                formatAmount(row.estimated_user_stepped_commission)
              }}</span>
            </div>
            <div class="estimated-user-stepped-commission">
              <span class="text-secondary">机构 </span>
              <span class="estimated-user-stepped-commission-value">{{
                formatAmount(row.estimated_inst_stepped_commission)
              }}</span>
            </div>
          </template>
          <!-- 结算佣金收入 -->
          <template #real_comission="{ row }">
            <div class="real-comission">
              <span class="text-secondary">达人 </span>
              <span class="real-comission-value">{{
                formatAmount(row.settle_user_comission)
              }}</span>
            </div>
            <div class="real-comission">
              <span class="text-secondary">机构 </span>
              <span class="real-comission-value">{{
                formatAmount(row.settle_inst_comission)
              }}</span>
            </div>
          </template>
          <!-- 结算奖励佣金收入 -->
          <template #estimated_user_comission="{ row }">
            <div class="estimated-user-comission">
              <span class="text-secondary">达人 </span>
              <span class="estimated-user-comission-value">{{
                formatAmount(row.settle_user_stepped_commission)
              }}</span>
            </div>
            <div class="estimated-user-comission">
              <span class="text-secondary">机构 </span>
              <span class="estimated-user-comission-value">{{
                formatAmount(row.settle_inst_stepped_commission)
              }}</span>
            </div>
          </template>
          <!-- 订单时间 -->
          <template #time="{ row }">
            <div class="time">
              <span class="text-secondary">付款 </span>
              <span class="time-value">{{ formatTimestamp(row.pay_time) }}</span>
            </div>
            <div class="time">
              <span class="text-secondary">成交 </span>
              <span class="time-value">{{ formatTimestamp(row.confirm_time) }}</span>
            </div>
            <div class="time">
              <span class="text-secondary">预计结算 </span>
              <span class="time-value">{{ row.estimated_settle_time || '-' }}</span>
            </div>
            <div class="time">
              <span class="text-secondary">结算 </span>
              <span class="time-value">{{ formatTimestamp(row.settle_time) }}</span>
            </div>
          </template>
        </DragableTable>
      </div>
    </div>

    <!-- Excel导入模态框 -->
    <ExcelImportModal v-model="showImportModal" @success="handleImportSuccess" />

    <!-- 自动导入模态框 -->
    <AutoImportModal v-model="showAutoImportModal" @success="handleImportSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, shallowRef } from 'vue'
import { useRoute } from 'vue-router'
import {
  ElDatePicker,
  ElSelect,
  ElOption,
  ElAutocomplete,
  ElButton,
  ElTag,
  ElTooltip
} from 'element-plus'
import DragableTable from '@components/common/dragableTable/dragableTable.vue'
import OrderMetrics from './OrderMetrics.vue'
import ExcelImportModal from './ExcelImportModal.vue'
import AutoImportModal from './AutoImportModal.vue'
import type { ProductMetricsData } from '../composables/useOrderMetrics'

// 路由
const route = useRoute()

// 订单数据 - 使用shallowRef减少响应式开销
const orderList = shallowRef<any[]>([])

// 简化的加载状态管理
const loading = ref(false)
const metricsLoading = ref(false)

// 综合加载状态
const isLoading = computed(() => loading.value || metricsLoading.value)

// 分页状态管理
const currentPage = ref(1)
const pageSize = ref(10)
const totalOrders = ref(0)

// 商品数据和搜索
const productList = shallowRef<any[]>([])
const searchQuery = ref('')
const statusFilter = ref('')

// 时间范围筛选（datetime）
const dateTimeRange = ref<string[]>([])

// 流量渠道筛选
const channelFilter = ref('')

// 精确时间筛选（时间戳）- 来自直播分析页面
const startTime = ref<number | null>(null)
const endTime = ref<number | null>(null)

// Excel导入模态框
const showImportModal = ref(false)
const showAutoImportModal = ref(false)

// 搜索缓存 - 带大小限制和智能清理
const searchCache = new Map<string, any[]>()
const MAX_CACHE_SIZE = 50 // 最大缓存50个搜索结果
const MIN_QUERY_LENGTH = 2 // 最少2个字符才缓存

// 防抖定时器引用
const searchTimeoutRef = ref<any>(null)
const searchDebounceRef = ref<any>(null)

// 清理缓存策略（LRU - 最近最少使用）
const cleanupCache = (): void => {
  if (searchCache.size >= MAX_CACHE_SIZE) {
    const firstKey = searchCache.keys().next().value as string
    if (firstKey) {
      searchCache.delete(firstKey)
    }
  }
}

// Element UI Autocomplete 异步搜索函数 - 优化缓存策略
const querySearchAsync = (queryString: string, cb: (results: any[]) => void): void => {
  if (!queryString) {
    cb([])
    return
  }

  const cacheKey = queryString.toLowerCase()

  // 检查缓存 - 优先使用缓存
  if (searchCache.has(cacheKey)) {
    const cachedResults = searchCache.get(cacheKey)!
    // 更新缓存访问顺序（LRU）
    searchCache.delete(cacheKey)
    searchCache.set(cacheKey, cachedResults)
    cb(cachedResults)
    return
  }

  // 清理之前的定时器
  if (searchTimeoutRef.value) {
    clearTimeout(searchTimeoutRef.value)
  }

  // 防抖处理
  searchTimeoutRef.value = setTimeout(() => {
    const results = productList.value
      .filter((product) => product.product_name.toLowerCase().includes(cacheKey))
      .slice(0, 10) // 限制显示10个结果
      .map((product) => ({
        ...product,
        value: product.product_name // Element UI Autocomplete 需要 value 字段
      }))

    // 智能缓存：只缓存长度>=2的搜索词，避免单字符造成过多缓存
    if (cacheKey.length >= MIN_QUERY_LENGTH) {
      cleanupCache() // 检查并清理缓存
      searchCache.set(cacheKey, results)
    }

    cb(results)
  }, 200) // 200ms防抖
}

// 处理分页事件
const handlePageChange = (page: number): void => {
  currentPage.value = page
  fetchOrderData(page, pageSize.value)
}

const handlePageSizeChange = (size: number): void => {
  pageSize.value = size
  currentPage.value = 1 // 重置到第一页
  fetchOrderData(1, size)
}

// 订单统计指标数据（从后端接口获取）
const currentMetrics = ref<ProductMetricsData>({
  totalPayAmount: 0,
  totalRefundAmount: 0,
  totalOrderCount: 0,
  refundOrderCount: 0
})

// 获取订单统计指标
const fetchOrderMetrics = async (): Promise<void> => {
  metricsLoading.value = true

  try {
    // 构建筛选条件
    const filters: any = {}

    // 商品名称搜索
    const searchTerm = searchQuery.value.trim()
    if (searchTerm) {
      filters.productName = searchTerm
    }

    // 订单状态筛选
    if (statusFilter.value) {
      filters.orderStatus = statusFilter.value
    }

    // 流量渠道筛选
    if (channelFilter.value) {
      filters.channelFilter = channelFilter.value
    }

    // 时间筛选
    if (startTime.value !== null && endTime.value !== null) {
      // 使用精确时间戳
      filters.startTime = startTime.value
      filters.endTime = endTime.value
    } else if (dateTimeRange.value && dateTimeRange.value.length === 2) {
      // 使用日期时间范围
      const [startDateTime, endDateTime] = dateTimeRange.value
      filters.startTime = Math.floor(new Date(startDateTime).getTime() / 1000)
      filters.endTime = Math.floor(new Date(endDateTime).getTime() / 1000)
    }

    // 调用后端接口获取统计数据
    const metrics = await window.electron.ipcRenderer.invoke('lzy:getOrderMetrics', filters)
    currentMetrics.value = metrics || {
      totalPayAmount: 0,
      totalRefundAmount: 0,
      totalOrderCount: 0,
      refundOrderCount: 0
    }
  } catch (error) {
    console.error('获取订单统计指标失败:', error)
    currentMetrics.value = {
      totalPayAmount: 0,
      totalRefundAmount: 0,
      totalOrderCount: 0,
      refundOrderCount: 0
    }
  } finally {
    metricsLoading.value = false
  }
}
// 时间格式化缓存
const timeFormatCache = new Map<number, string>()

// 格式化时间戳函数 - 添加缓存
const formatTimestamp = (timestamp: number): string => {
  if (!timestamp || timestamp === 1) return '-'

  if (timeFormatCache.has(timestamp)) {
    return timeFormatCache.get(timestamp)!
  }

  const formatted = new Date(timestamp * 1000).toLocaleString()
  timeFormatCache.set(timestamp, formatted)
  return formatted
}

// 格式化金额函数
const formatAmount = (value: number): string => {
  return value ? `¥${(value / 100).toFixed(2)}` : '-'
}

// 清除所有筛选条件
const clearAllFilters = (): void => {
  searchQuery.value = ''
  statusFilter.value = ''
  channelFilter.value = ''
  dateTimeRange.value = []
  startTime.value = null
  endTime.value = null
  // 清除缓存
  searchCache.clear()
  timeFormatCache.clear()
  // 重置到第一页并重新获取数据
  currentPage.value = 1
  fetchOrderData()
  fetchOrderMetrics()
}

const formatOrderStatus = (status: string): string => {
  const sourceMap = { PAY_SUCC: '已付款', REFUND: '退货退款', CONFIRM: '已收货', SETTLE: '已结算' }
  return sourceMap[status] || status
}

// 表格列定义
const tableColumns = ref([
  {
    key: 'order_id',
    label: '订单ID',
    width: 200,
    fixed: 'left' as const,
    slotName: 'order_id'
  },
  {
    key: 'product_name',
    label: '商品详情',
    width: 200,
    slotName: 'product_detail'
  },
  {
    key: 'unsettled_event',
    label: '超时未结算原因',
    width: 150
  },
  {
    key: 'buyer_app_id',
    label: '下单来源',
    width: 100,
    format: (value: string) => {
      const sourceMap = {
        '13': '头条',
        '32': '西瓜',
        '1112': '火山',
        '1128': '抖音',
        '6340': '抖音盒子',
        '2329': '抖音极速版'
      }
      return sourceMap[value] || '其他'
    }
  },
  {
    key: 'media_type_group_name',
    label: '流量渠道',
    width: 100
  },
  {
    key: 'traffic_source',
    label: '流量细分渠道',
    width: 100
  },
  {
    key: 'user_name',
    label: '达人信息',
    width: 150
  },
  {
    key: 'total_pay_amount',
    label: '计佣金额',
    width: 120,
    format: formatAmount
  },
  {
    key: 'commission_rate',
    label: '佣金率',
    width: 100,
    format: (value: number) => `${(value / 100).toFixed(2)}%`
  },
  {
    key: 'total_commission',
    label: '总佣金',
    width: 120,
    format: formatAmount
  },
  {
    key: 'platform_service_fee',
    label: '预估技术服务费',
    width: 130,
    format: formatAmount
  },
  {
    key: 'user_profit_ratio',
    label: '分成比例',
    width: 140,
    slotName: 'user_profit_ratio'
  },
  {
    key: 'estimated_comission',
    label: '预估佣金收入',
    width: 130,
    slotName: 'estimated_comission'
  },
  {
    key: 'estimated_user_stepped_commission',
    label: '预估奖励佣金收入',
    width: 130,
    slotName: 'estimated_user_stepped_commission'
  },
  {
    key: 'real_comission',
    label: '结算佣金收入',
    width: 140,
    slotName: 'real_comission'
  },
  {
    key: 'estimated_user_comission',
    label: '结算奖励佣金收入',
    width: 140,
    slotName: 'estimated_user_comission'
  },
  {
    key: 'content_type',
    label: '订单类型',
    width: 140
  },
  {
    key: 'time',
    label: '订单时间',
    width: 250,
    slotName: 'time'
  }
])

// 方法
const handleProductSelect = (item: any): void => {
  searchQuery.value = item.product_name
  // 重置到第一页并重新获取数据
  currentPage.value = 1
  fetchOrderData()
  fetchOrderMetrics()
}

// 防抖搜索函数
const debouncedSearch = (): void => {
  // 清除之前的防抖定时器
  if (searchDebounceRef.value) {
    clearTimeout(searchDebounceRef.value)
  }

  // 设置新的防抖定时器
  searchDebounceRef.value = setTimeout(() => {
    currentPage.value = 1
    fetchOrderData()
    fetchOrderMetrics()
  }, 500) // 500ms 防抖延迟
}

const handleFilter = (): void => {
  // 重置到第一页并重新获取数据
  currentPage.value = 1
  fetchOrderData()
  fetchOrderMetrics()
}

const handleDateTimeFilter = (): void => {
  // 当用户手动修改时间时，清除来自直播分析页面的精确时间戳
  startTime.value = null
  endTime.value = null
  // 重置到第一页并重新获取数据
  currentPage.value = 1
  fetchOrderData()
  fetchOrderMetrics()
}

const handleChannelFilter = (): void => {
  // 重置到第一页并重新获取数据
  currentPage.value = 1
  fetchOrderData()
  fetchOrderMetrics()
}

const handleImportSuccess = (): void => {
  // 导入成功后刷新数据
  fetchOrderData()
}

const fetchProductList = async (): Promise<void> => {
  try {
    // 获取所有商品用于搜索（不分页，或使用大页面大小）
    const response = await window.electron.ipcRenderer.invoke('getProducts', {
      pageSize: 1000 // 获取足够多的商品用于搜索
    })
    productList.value = response?.data || []
  } catch {
    productList.value = []
  }
}

const fetchOrderData = async (page?: number, size?: number): Promise<void> => {
  loading.value = true

  try {
    // 构建查询参数
    const params: any = {
      page: page || currentPage.value,
      pageSize: size || pageSize.value
    }

    // 添加筛选条件
    const searchTerm = searchQuery.value.trim()
    if (searchTerm) {
      params.productName = searchTerm
    }

    if (statusFilter.value) {
      params.order_status = statusFilter.value
    }

    if (channelFilter.value) {
      params.channelFilter = channelFilter.value
    }

    // 时间筛选
    if (startTime.value !== null && endTime.value !== null) {
      params.start_time = startTime.value
      params.end_time = endTime.value
    } else if (dateTimeRange.value && dateTimeRange.value.length === 2) {
      const [startDateTime, endDateTime] = dateTimeRange.value
      params.start_time = Math.floor(new Date(startDateTime).getTime() / 1000)
      params.end_time = Math.floor(new Date(endDateTime).getTime() / 1000)
    }

    // 调用IPC接口获取订单数据
    const response = await window.electron.ipcRenderer.invoke('lzy:getOrderList', params)
    orderList.value = response?.data || []
    totalOrders.value = response?.total || 0
    currentPage.value = response?.page || 1
    pageSize.value = response?.pageSize || 10
  } catch (error) {
    console.error('获取订单数据失败:', error)
    orderList.value = []
    totalOrders.value = 0
  } finally {
    loading.value = false
  }
}

// 应用路由state传递的筛选条件
const applyRouteFilters = (): void => {
  const routeState = history.state

  // 检查state中是否有我们需要的参数（刷新后state会自动清除）
  if (!routeState?.startTime || !routeState?.endTime) {
    return
  }

  const { channel, startTime: routeStartTime, endTime: routeEndTime } = routeState

  if (channel) {
    channelFilter.value = channel
  }

  // 设置精确时间筛选
  startTime.value = routeStartTime
  endTime.value = routeEndTime

  // 将时间戳转换为datetime字符串用于UI显示
  const startDateTime = new Date(routeStartTime * 1000)
  const endDateTime = new Date(routeEndTime * 1000)

  const formatDateTime = (date: Date): string => {
    return date.toISOString().slice(0, 19).replace('T', ' ')
  }

  dateTimeRange.value = [formatDateTime(startDateTime), formatDateTime(endDateTime)]
}

// 应用来自商品页面的搜索条件
const applyProductSearchParams = (): void => {
  const { productSearch } = route.query

  if (productSearch) {
    // 设置商品搜索条件
    searchQuery.value = productSearch as string
  }
}

// 页面加载时获取数据
onMounted(async () => {
  loading.value = true

  try {
    // 并行获取数据以提高性能
    await Promise.all([fetchOrderData(), fetchProductList(), fetchOrderMetrics()])

    // 应用路由筛选条件（只需要在挂载时检查一次state）
    applyRouteFilters()
    // 应用来自商品页面的搜索条件
    applyProductSearchParams()
  } finally {
    loading.value = false
  }
})

// 组件卸载时清理资源，防止内存泄漏
onBeforeUnmount(() => {
  // 清理防抖定时器
  if (searchTimeoutRef.value) {
    clearTimeout(searchTimeoutRef.value)
    searchTimeoutRef.value = null
  }

  // 清理搜索防抖定时器
  if (searchDebounceRef.value) {
    clearTimeout(searchDebounceRef.value)
    searchDebounceRef.value = null
  }

  // 清理所有缓存
  searchCache.clear()
  timeFormatCache.clear()

  // 清空大数据数组
  orderList.value = []
  productList.value = []
})
</script>

<style scoped>
.card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid var(--bs-border-color);
  padding: 1.5rem;
}

.card-body {
  padding: 1.5rem;
}

/* Element UI 组件基础样式 */
:deep(.el-input__wrapper) {
  border-radius: var(--bs-border-radius);
}

:deep(.el-select .el-input__wrapper) {
  border-radius: var(--bs-border-radius);
}

:deep(.el-date-editor) {
  border-radius: var(--bs-border-radius);
}
</style>
